#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
风电数据集独立分析工具
生成数据可视化热图、分布图、相关性分析等
完全独立，不依赖其他文件
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
import os
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
import matplotlib.dates as mdates

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300

class WindDataAnalyzer:
    """风电数据分析器"""
    
    def __init__(self, data_path: str):
        """
        初始化分析器
        
        Args:
            data_path: CSV数据文件路径
        """
        self.data_path = data_path
        self.df = None
        self.numeric_cols = []
        self.time_col = None
        self.power_col = None
        
        # 创建输出目录
        self.output_dir = "wind_data_analysis_results"
        os.makedirs(self.output_dir, exist_ok=True)
        
        print(f"📊 风电数据分析器初始化完成")
        print(f"📁 结果将保存到: {self.output_dir}")
    
    def load_data(self):
        """加载数据"""
        try:
            print(f"📖 正在加载数据: {self.data_path}")
            
            # 尝试多种编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1']
            for encoding in encodings:
                try:
                    self.df = pd.read_csv(self.data_path, encoding=encoding)
                    print(f"✅ 使用 {encoding} 编码成功加载数据")
                    break
                except UnicodeDecodeError:
                    continue
            
            if self.df is None:
                raise ValueError("无法使用任何编码加载数据")
            
            print(f"📊 数据形状: {self.df.shape}")
            print(f"📋 数据列: {list(self.df.columns)}")
            
            # 识别列类型
            self._identify_columns()
            
            # 数据预处理
            self._preprocess_data()
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {str(e)}")
            return False
    
    def _identify_columns(self):
        """识别列类型"""
        # 识别时间列
        for col in self.df.columns:
            if 'time' in col.lower() or 'date' in col.lower():
                self.time_col = col
                break
        
        # 识别功率列
        for col in self.df.columns:
            if 'power' in col.lower():
                self.power_col = col
                break
        
        # 识别数值列
        self.numeric_cols = self.df.select_dtypes(include=[np.number]).columns.tolist()
        
        print(f"🕐 时间列: {self.time_col}")
        print(f"⚡ 功率列: {self.power_col}")
        print(f"🔢 数值列数量: {len(self.numeric_cols)}")
    
    def _preprocess_data(self):
        """数据预处理"""
        # 处理时间列
        if self.time_col:
            try:
                self.df[self.time_col] = pd.to_datetime(self.df[self.time_col])
                print(f"✅ 时间列转换成功")
            except:
                print(f"⚠️ 时间列转换失败")
        
        # 处理缺失值
        missing_info = self.df.isnull().sum()
        if missing_info.sum() > 0:
            print(f"⚠️ 发现缺失值:")
            for col, count in missing_info[missing_info > 0].items():
                print(f"   {col}: {count} ({count/len(self.df)*100:.2f}%)")
            
            # 数值列用均值填充
            for col in self.numeric_cols:
                if self.df[col].isnull().sum() > 0:
                    self.df[col].fillna(self.df[col].mean(), inplace=True)
    
    def generate_basic_info(self):
        """生成基础信息报告"""
        print("\n" + "="*50)
        print("📊 数据集基础信息")
        print("="*50)
        
        info_dict = {
            '数据形状': f"{self.df.shape[0]} 行 × {self.df.shape[1]} 列",
            '时间范围': f"{self.df[self.time_col].min()} 到 {self.df[self.time_col].max()}" if self.time_col else "无时间信息",
            '数据时长': f"{(self.df[self.time_col].max() - self.df[self.time_col].min()).days} 天" if self.time_col else "无时间信息",
            '采样频率': "10分钟" if len(self.df) > 50000 else "未知",
            '功率范围': f"{self.df[self.power_col].min():.2f} - {self.df[self.power_col].max():.2f} MW" if self.power_col else "无功率信息"
        }
        
        for key, value in info_dict.items():
            print(f"{key}: {value}")
        
        # 保存基础统计信息
        desc_stats = self.df[self.numeric_cols].describe()
        desc_stats.to_csv(f"{self.output_dir}/基础统计信息.csv", encoding='utf-8-sig')
        print(f"✅ 基础统计信息已保存")
    
    def plot_correlation_heatmap(self):
        """绘制相关性热图"""
        plt.figure(figsize=(14, 12))
        
        # 计算相关性矩阵
        corr_matrix = self.df[self.numeric_cols].corr()
        
        # 创建热图
        mask = np.triu(np.ones_like(corr_matrix, dtype=bool))
        sns.heatmap(corr_matrix, 
                   mask=mask,
                   annot=True, 
                   cmap='RdYlBu_r', 
                   center=0,
                   square=True,
                   fmt='.2f',
                   cbar_kws={"shrink": .8})
        
        plt.title('特征相关性热图', fontsize=16, fontweight='bold', pad=20)
        plt.xticks(rotation=45, ha='right')
        plt.yticks(rotation=0)
        plt.tight_layout()
        
        plt.savefig(f"{self.output_dir}/相关性热图.png", dpi=300, bbox_inches='tight')
        plt.close()
        print(f"✅ 相关性热图已保存")
    
    def plot_power_analysis(self):
        """功率分析图表"""
        if not self.power_col:
            print("⚠️ 未找到功率列，跳过功率分析")
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        
        # 1. 功率分布直方图
        axes[0, 0].hist(self.df[self.power_col], bins=50, alpha=0.7, color='skyblue', edgecolor='black')
        axes[0, 0].set_title('功率分布直方图', fontweight='bold')
        axes[0, 0].set_xlabel('功率 (MW)')
        axes[0, 0].set_ylabel('频次')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 功率箱线图
        axes[0, 1].boxplot(self.df[self.power_col], patch_artist=True,
                          boxprops=dict(facecolor='lightgreen', alpha=0.7))
        axes[0, 1].set_title('功率箱线图', fontweight='bold')
        axes[0, 1].set_ylabel('功率 (MW)')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 功率时间序列（如果有时间列）
        if self.time_col:
            # 采样显示（避免数据点过多）
            sample_size = min(5000, len(self.df))
            sample_idx = np.random.choice(len(self.df), sample_size, replace=False)
            sample_df = self.df.iloc[sample_idx].sort_values(self.time_col)
            
            axes[1, 0].plot(sample_df[self.time_col], sample_df[self.power_col], 
                           alpha=0.6, linewidth=0.5, color='red')
            axes[1, 0].set_title('功率时间序列（采样显示）', fontweight='bold')
            axes[1, 0].set_xlabel('时间')
            axes[1, 0].set_ylabel('功率 (MW)')
            axes[1, 0].grid(True, alpha=0.3)
            
            # 格式化x轴
            axes[1, 0].xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
            axes[1, 0].xaxis.set_major_locator(mdates.MonthLocator(interval=2))
            plt.setp(axes[1, 0].xaxis.get_majorticklabels(), rotation=45)
        else:
            axes[1, 0].text(0.5, 0.5, '无时间信息', ha='center', va='center', 
                           transform=axes[1, 0].transAxes, fontsize=14)
            axes[1, 0].set_title('功率时间序列', fontweight='bold')
        
        # 4. 功率统计信息
        power_stats = {
            '平均值': f"{self.df[self.power_col].mean():.2f} MW",
            '中位数': f"{self.df[self.power_col].median():.2f} MW",
            '标准差': f"{self.df[self.power_col].std():.2f} MW",
            '最小值': f"{self.df[self.power_col].min():.2f} MW",
            '最大值': f"{self.df[self.power_col].max():.2f} MW",
            '容量因子': f"{self.df[self.power_col].mean()/self.df[self.power_col].max()*100:.1f}%"
        }
        
        axes[1, 1].axis('off')
        y_pos = 0.9
        axes[1, 1].text(0.1, y_pos, '功率统计信息', fontsize=14, fontweight='bold',
                       transform=axes[1, 1].transAxes)
        
        for i, (key, value) in enumerate(power_stats.items()):
            y_pos -= 0.12
            axes[1, 1].text(0.1, y_pos, f"{key}: {value}", fontsize=12,
                           transform=axes[1, 1].transAxes)
        
        plt.suptitle('功率特性分析', fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        plt.savefig(f"{self.output_dir}/功率分析.png", dpi=300, bbox_inches='tight')
        plt.close()
        print(f"✅ 功率分析图已保存")
