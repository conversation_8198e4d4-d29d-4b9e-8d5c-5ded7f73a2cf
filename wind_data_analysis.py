#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
风电数据集独立分析工具
生成数据可视化热图、分布图、相关性分析等
完全独立，不依赖其他文件
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
import os
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
import matplotlib.dates as mdates

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300

class WindDataAnalyzer:
    """风电数据分析器"""
    
    def __init__(self, data_path: str):
        """
        初始化分析器
        
        Args:
            data_path: CSV数据文件路径
        """
        self.data_path = data_path
        self.df = None
        self.numeric_cols = []
        self.time_col = None
        self.power_col = None
        
        # 创建输出目录
        self.output_dir = "wind_data_analysis_results"
        os.makedirs(self.output_dir, exist_ok=True)
        
        print(f"📊 风电数据分析器初始化完成")
        print(f"📁 结果将保存到: {self.output_dir}")
    
    def load_data(self):
        """加载数据"""
        try:
            print(f"📖 正在加载数据: {self.data_path}")
            
            # 尝试多种编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1']
            for encoding in encodings:
                try:
                    self.df = pd.read_csv(self.data_path, encoding=encoding)
                    print(f"✅ 使用 {encoding} 编码成功加载数据")
                    break
                except UnicodeDecodeError:
                    continue
            
            if self.df is None:
                raise ValueError("无法使用任何编码加载数据")
            
            print(f"📊 数据形状: {self.df.shape}")
            print(f"📋 数据列: {list(self.df.columns)}")
            
            # 识别列类型
            self._identify_columns()
            
            # 数据预处理
            self._preprocess_data()
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {str(e)}")
            return False
    
    def _identify_columns(self):
        """识别列类型"""
        # 识别时间列
        for col in self.df.columns:
            if 'time' in col.lower() or 'date' in col.lower():
                self.time_col = col
                break
        
        # 识别功率列
        for col in self.df.columns:
            if 'power' in col.lower():
                self.power_col = col
                break
        
        # 识别数值列
        self.numeric_cols = self.df.select_dtypes(include=[np.number]).columns.tolist()
        
        print(f"🕐 时间列: {self.time_col}")
        print(f"⚡ 功率列: {self.power_col}")
        print(f"🔢 数值列数量: {len(self.numeric_cols)}")
    
    def _preprocess_data(self):
        """数据预处理"""
        # 处理时间列
        if self.time_col:
            try:
                self.df[self.time_col] = pd.to_datetime(self.df[self.time_col])
                print(f"✅ 时间列转换成功")
            except:
                print(f"⚠️ 时间列转换失败")
        
        # 处理缺失值
        missing_info = self.df.isnull().sum()
        if missing_info.sum() > 0:
            print(f"⚠️ 发现缺失值:")
            for col, count in missing_info[missing_info > 0].items():
                print(f"   {col}: {count} ({count/len(self.df)*100:.2f}%)")
            
            # 数值列用均值填充
            for col in self.numeric_cols:
                if self.df[col].isnull().sum() > 0:
                    self.df[col].fillna(self.df[col].mean(), inplace=True)
    
    def generate_basic_info(self):
        """生成基础信息报告"""
        print("\n" + "="*50)
        print("📊 数据集基础信息")
        print("="*50)
        
        # 安全地处理时间信息
        time_info = "无时间信息"
        duration_info = "无时间信息"

        if self.time_col and pd.api.types.is_datetime64_any_dtype(self.df[self.time_col]):
            time_min = self.df[self.time_col].min()
            time_max = self.df[self.time_col].max()
            time_info = f"{time_min} 到 {time_max}"
            duration_info = f"{(time_max - time_min).days} 天"
        elif self.time_col:
            time_info = f"时间列存在但未转换为日期格式"
            duration_info = "无法计算时长"

        info_dict = {
            '数据形状': f"{self.df.shape[0]} 行 × {self.df.shape[1]} 列",
            '时间范围': time_info,
            '数据时长': duration_info,
            '采样频率': "10分钟" if len(self.df) > 50000 else "未知",
            '功率范围': f"{self.df[self.power_col].min():.2f} - {self.df[self.power_col].max():.2f} MW" if self.power_col else "无功率信息"
        }
        
        for key, value in info_dict.items():
            print(f"{key}: {value}")
        
        # 保存基础统计信息
        desc_stats = self.df[self.numeric_cols].describe()
        desc_stats.to_csv(f"{self.output_dir}/基础统计信息.csv", encoding='utf-8-sig')
        print(f"✅ 基础统计信息已保存")
    
    def plot_correlation_heatmap(self):
        """绘制相关性热图"""
        # 创建更大的图形
        plt.figure(figsize=(20, 16))

        # 计算相关性矩阵
        corr_matrix = self.df[self.numeric_cols].corr()

        # 简化列名以便显示（英文标签）
        simplified_cols = []
        for col in self.numeric_cols:
            if 'Wind speed' in col:
                if '10 meters' in col:
                    simplified_cols.append('WS_10m')
                elif '30 meters' in col:
                    simplified_cols.append('WS_30m')
                elif '50 meters' in col:
                    simplified_cols.append('WS_50m')
                elif 'wheel hub' in col:
                    simplified_cols.append('WS_Hub')
                else:
                    simplified_cols.append('WindSpeed')
            elif 'Wind direction' in col:
                if '10 meters' in col:
                    simplified_cols.append('WD_10m')
                elif '30 meters' in col:
                    simplified_cols.append('WD_30m')
                elif '50 meters' in col:
                    simplified_cols.append('WD_50m')
                elif 'wheel hub' in col:
                    simplified_cols.append('WD_Hub')
                else:
                    simplified_cols.append('WindDir')
            elif 'temperature' in col.lower():
                simplified_cols.append('Temperature')
            elif 'humidity' in col.lower():
                simplified_cols.append('Humidity')
            elif 'atmosphere' in col.lower():
                simplified_cols.append('Pressure')
            elif 'Power' in col:
                simplified_cols.append('Power')
            else:
                simplified_cols.append(col[:8])  # 截断长名称

        # 创建带简化标签的相关性矩阵
        corr_matrix.index = simplified_cols
        corr_matrix.columns = simplified_cols

        # 创建热图 - 不使用mask，显示完整矩阵
        sns.heatmap(corr_matrix,
                   annot=True,
                   cmap='RdYlBu_r',
                   center=0,
                   square=True,
                   fmt='.2f',
                   annot_kws={'size': 10},  # 增大注释字体
                   cbar_kws={"shrink": .8, "label": "Correlation Coefficient"})

        plt.title('Feature Correlation Heatmap', fontsize=20, fontweight='bold', pad=30)
        plt.xticks(rotation=45, ha='right', fontsize=12)
        plt.yticks(rotation=0, fontsize=12)

        # 调整布局，确保标签完整显示
        plt.tight_layout()

        plt.savefig(f"{self.output_dir}/correlation_heatmap.png", dpi=300, bbox_inches='tight',
                   facecolor='white', edgecolor='none')
        plt.close()
        print(f"✅ Correlation heatmap saved")
    
    def plot_power_analysis(self):
        """功率分析图表"""
        if not self.power_col:
            print("⚠️ 未找到功率列，跳过功率分析")
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        
        # 1. 功率分布直方图
        axes[0, 0].hist(self.df[self.power_col], bins=50, alpha=0.7, color='skyblue', edgecolor='black')
        axes[0, 0].set_title('Power Distribution', fontweight='bold')
        axes[0, 0].set_xlabel('Power (MW)')
        axes[0, 0].set_ylabel('Frequency')
        axes[0, 0].grid(True, alpha=0.3)

        # 2. 功率箱线图
        axes[0, 1].boxplot(self.df[self.power_col], patch_artist=True,
                          boxprops=dict(facecolor='lightgreen', alpha=0.7))
        axes[0, 1].set_title('Power Box Plot', fontweight='bold')
        axes[0, 1].set_ylabel('Power (MW)')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 功率时间序列（如果有时间列）
        if self.time_col:
            # 采样显示（避免数据点过多）
            sample_size = min(5000, len(self.df))
            sample_idx = np.random.choice(len(self.df), sample_size, replace=False)
            sample_df = self.df.iloc[sample_idx].sort_values(self.time_col)
            
            axes[1, 0].plot(sample_df[self.time_col], sample_df[self.power_col],
                           alpha=0.6, linewidth=0.5, color='red')
            axes[1, 0].set_title('Power Time Series (Sampled)', fontweight='bold')
            axes[1, 0].set_xlabel('Time')
            axes[1, 0].set_ylabel('Power (MW)')
            axes[1, 0].grid(True, alpha=0.3)
            
            # 格式化x轴
            axes[1, 0].xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
            axes[1, 0].xaxis.set_major_locator(mdates.MonthLocator(interval=2))
            plt.setp(axes[1, 0].xaxis.get_majorticklabels(), rotation=45)
        else:
            axes[1, 0].text(0.5, 0.5, 'No Time Information', ha='center', va='center',
                           transform=axes[1, 0].transAxes, fontsize=14)
            axes[1, 0].set_title('Power Time Series', fontweight='bold')
        
        # 4. 功率统计信息
        power_stats = {
            'Mean': f"{self.df[self.power_col].mean():.2f} MW",
            'Median': f"{self.df[self.power_col].median():.2f} MW",
            'Std Dev': f"{self.df[self.power_col].std():.2f} MW",
            'Min': f"{self.df[self.power_col].min():.2f} MW",
            'Max': f"{self.df[self.power_col].max():.2f} MW",
            'Capacity Factor': f"{self.df[self.power_col].mean()/self.df[self.power_col].max()*100:.1f}%"
        }

        axes[1, 1].axis('off')
        y_pos = 0.9
        axes[1, 1].text(0.1, y_pos, 'Power Statistics', fontsize=14, fontweight='bold',
                       transform=axes[1, 1].transAxes)
        
        for i, (key, value) in enumerate(power_stats.items()):
            y_pos -= 0.12
            axes[1, 1].text(0.1, y_pos, f"{key}: {value}", fontsize=12,
                           transform=axes[1, 1].transAxes)
        
        plt.suptitle('Power Analysis', fontsize=16, fontweight='bold')
        plt.tight_layout()

        plt.savefig(f"{self.output_dir}/power_analysis.png", dpi=300, bbox_inches='tight')
        plt.close()
        print(f"✅ Power analysis plot saved")

    def plot_wind_analysis(self):
        """风速风向分析"""
        # 查找风速和风向列
        wind_speed_cols = [col for col in self.df.columns if 'wind speed' in col.lower()]
        wind_dir_cols = [col for col in self.df.columns if 'wind direction' in col.lower()]

        if not wind_speed_cols:
            print("⚠️ 未找到风速列，跳过风速分析")
            return

        fig, axes = plt.subplots(2, 2, figsize=(16, 12))

        # 1. 风速分布对比
        for i, col in enumerate(wind_speed_cols[:3]):  # 最多显示3个风速
            axes[0, 0].hist(self.df[col], bins=30, alpha=0.6,
                           label=col.replace('Wind speed at height of ', '').replace(' meters (m/s)', 'm'))
        axes[0, 0].set_title('Wind Speed Distribution by Height', fontweight='bold')
        axes[0, 0].set_xlabel('Wind Speed (m/s)')
        axes[0, 0].set_ylabel('Frequency')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # 2. 风速与功率关系
        if self.power_col and wind_speed_cols:
            main_wind_col = wind_speed_cols[0]  # 使用第一个风速列
            # 采样显示
            sample_size = min(2000, len(self.df))
            sample_idx = np.random.choice(len(self.df), sample_size, replace=False)

            axes[0, 1].scatter(self.df.iloc[sample_idx][main_wind_col],
                              self.df.iloc[sample_idx][self.power_col],
                              alpha=0.5, s=10, color='red')
            axes[0, 1].set_title('Wind Speed vs Power', fontweight='bold')
            axes[0, 1].set_xlabel('Wind Speed (m/s)')
            axes[0, 1].set_ylabel('Power (MW)')
            axes[0, 1].grid(True, alpha=0.3)

        # 3. 风向玫瑰图（简化版）
        if wind_dir_cols:
            wind_dir_col = wind_dir_cols[0]
            # 将风向分为16个方向
            dir_bins = np.arange(0, 361, 22.5)
            dir_labels = ['N', 'NNE', 'NE', 'ENE', 'E', 'ESE', 'SE', 'SSE',
                         'S', 'SSW', 'SW', 'WSW', 'W', 'WNW', 'NW', 'NNW']

            wind_dir_binned = pd.cut(self.df[wind_dir_col], bins=dir_bins, labels=dir_labels, include_lowest=True)
            dir_counts = wind_dir_binned.value_counts().sort_index()

            # 简化的风向分布柱状图
            axes[1, 0].bar(range(len(dir_counts)), dir_counts.values, color='lightblue', alpha=0.7)
            axes[1, 0].set_title('风向分布', fontweight='bold')
            axes[1, 0].set_xlabel('风向')
            axes[1, 0].set_ylabel('频次')
            axes[1, 0].set_xticks(range(len(dir_counts)))
            axes[1, 0].set_xticklabels(dir_counts.index, rotation=45)
            axes[1, 0].grid(True, alpha=0.3)

        # 4. 风速统计信息
        axes[1, 1].axis('off')
        y_pos = 0.9
        axes[1, 1].text(0.1, y_pos, '风速统计信息', fontsize=14, fontweight='bold',
                       transform=axes[1, 1].transAxes)

        for col in wind_speed_cols[:3]:
            y_pos -= 0.15
            height = col.replace('Wind speed at height of ', '').replace(' meters (m/s)', 'm')
            mean_speed = self.df[col].mean()
            axes[1, 1].text(0.1, y_pos, f"{height}: {mean_speed:.2f} m/s", fontsize=12,
                           transform=axes[1, 1].transAxes)

        plt.suptitle('Wind Characteristics Analysis', fontsize=16, fontweight='bold')
        plt.tight_layout()

        plt.savefig(f"{self.output_dir}/wind_analysis.png", dpi=300, bbox_inches='tight')
        plt.close()
        print(f"✅ Wind analysis plot saved")

    def plot_environmental_analysis(self):
        """环境因素分析"""
        # 查找环境相关列
        temp_cols = [col for col in self.df.columns if 'temperature' in col.lower()]
        humidity_cols = [col for col in self.df.columns if 'humidity' in col.lower()]
        pressure_cols = [col for col in self.df.columns if 'atmosphere' in col.lower() or 'pressure' in col.lower()]

        env_cols = temp_cols + humidity_cols + pressure_cols

        if not env_cols:
            print("⚠️ 未找到环境因素列，跳过环境分析")
            return

        fig, axes = plt.subplots(2, 2, figsize=(16, 12))

        # 1. 环境因素分布
        for i, col in enumerate(env_cols[:4]):  # 最多显示4个环境因素
            row, col_idx = i // 2, i % 2
            if row < 2 and col_idx < 2:
                axes[row, col_idx].hist(self.df[col], bins=30, alpha=0.7, color=f'C{i}')
                axes[row, col_idx].set_title(col.replace(' (°C) ', '').replace(' (%)', '').replace(' (hpa)', ''),
                                           fontweight='bold')
                axes[row, col_idx].set_ylabel('频次')
                axes[row, col_idx].grid(True, alpha=0.3)

        # 如果环境因素少于4个，隐藏多余的子图
        for i in range(len(env_cols), 4):
            row, col_idx = i // 2, i % 2
            axes[row, col_idx].axis('off')

        plt.suptitle('Environmental Factors Analysis', fontsize=16, fontweight='bold')
        plt.tight_layout()

        plt.savefig(f"{self.output_dir}/environmental_analysis.png", dpi=300, bbox_inches='tight')
        plt.close()
        print(f"✅ Environmental analysis plot saved")

    def plot_pca_analysis(self):
        """主成分分析"""
        if len(self.numeric_cols) < 3:
            print("⚠️ 数值列太少，跳过PCA分析")
            return

        # 数据标准化
        scaler = StandardScaler()
        scaled_data = scaler.fit_transform(self.df[self.numeric_cols])

        # PCA分析
        pca = PCA()
        pca_result = pca.fit_transform(scaled_data)

        fig, axes = plt.subplots(1, 2, figsize=(16, 6))

        # 1. 方差解释比例
        cumsum_ratio = np.cumsum(pca.explained_variance_ratio_)
        axes[0].bar(range(1, len(pca.explained_variance_ratio_) + 1),
                   pca.explained_variance_ratio_, alpha=0.7, color='skyblue')
        axes[0].plot(range(1, len(cumsum_ratio) + 1), cumsum_ratio, 'ro-', color='red')
        axes[0].set_title('Principal Component Variance Explained', fontweight='bold')
        axes[0].set_xlabel('Principal Component')
        axes[0].set_ylabel('Variance Explained Ratio')
        axes[0].grid(True, alpha=0.3)

        # 2. 前两个主成分散点图
        if self.power_col:
            scatter = axes[1].scatter(pca_result[:, 0], pca_result[:, 1],
                                    c=self.df[self.power_col], cmap='viridis', alpha=0.6, s=10)
            plt.colorbar(scatter, ax=axes[1], label='Power (MW)')
        else:
            axes[1].scatter(pca_result[:, 0], pca_result[:, 1], alpha=0.6, s=10)

        axes[1].set_title('First Two Principal Components', fontweight='bold')
        axes[1].set_xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.1%})')
        axes[1].set_ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.1%})')
        axes[1].grid(True, alpha=0.3)

        plt.tight_layout()

        plt.savefig(f"{self.output_dir}/pca_analysis.png", dpi=300, bbox_inches='tight')
        plt.close()
        print(f"✅ PCA analysis plot saved")

    def generate_summary_report(self):
        """生成分析总结报告"""
        report_lines = [
            "# 风电数据分析报告",
            f"**分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"**数据文件**: {self.data_path}",
            "",
            "## 数据概览",
            f"- 数据形状: {self.df.shape[0]} 行 × {self.df.shape[1]} 列",
            f"- 数值列数量: {len(self.numeric_cols)}",
            f"- 时间列: {self.time_col if self.time_col else '无'}",
            f"- 功率列: {self.power_col if self.power_col else '无'}",
            "",
            "## 生成的分析图表",
            "1. 相关性热图.png - 特征间相关性分析",
            "2. 功率分析.png - 功率特性详细分析",
            "3. 风况分析.png - 风速风向特性分析",
            "4. 环境因素分析.png - 温度、湿度、气压分析",
            "5. 主成分分析.png - 数据降维和特征重要性",
            "",
            "## 关键发现",
        ]

        # 添加关键统计信息
        if self.power_col:
            power_stats = self.df[self.power_col].describe()
            report_lines.extend([
                f"- 平均功率: {power_stats['mean']:.2f} MW",
                f"- 功率标准差: {power_stats['std']:.2f} MW",
                f"- 容量因子: {power_stats['mean']/power_stats['max']*100:.1f}%",
            ])

        # 保存报告
        with open(f"{self.output_dir}/分析报告.md", 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))

        print(f"✅ 分析报告已保存")

    def run_complete_analysis(self):
        """运行完整分析"""
        print("🚀 开始完整数据分析...")

        if not self.load_data():
            return False

        # 生成各种分析图表
        self.generate_basic_info()
        self.plot_correlation_heatmap()
        self.plot_power_analysis()
        self.plot_wind_analysis()
        self.plot_environmental_analysis()
        self.plot_pca_analysis()
        self.generate_summary_report()

        print(f"\n🎉 分析完成！所有结果已保存到: {self.output_dir}")
        return True


def main():
    """主函数"""
    # 优先级顺序的默认数据文件路径（只使用CSV文件）
    default_paths = [
        "data/raw/Wind farm site 6 (Nominal capacity-96MW).csv",
        "data/raw/Wind farm site 1 (Nominal capacity-99MW).csv",
        "data/raw/Site_1_standardized.csv"
    ]

    print("🌪️ Wind Power Data Analysis Tool")
    print("=" * 50)

    # 按优先级检查文件是否存在
    data_path = None
    for path in default_paths:
        if os.path.exists(path):
            data_path = path
            print(f"📁 Using default data file: {data_path}")
            break

    if data_path is None:
        # 让用户输入文件路径
        data_path = input("Please enter CSV data file path: ").strip()
        if not os.path.exists(data_path):
            print(f"❌ File not found: {data_path}")
            return

    # 创建分析器并运行分析
    analyzer = WindDataAnalyzer(data_path)
    analyzer.run_complete_analysis()


if __name__ == "__main__":
    main()
