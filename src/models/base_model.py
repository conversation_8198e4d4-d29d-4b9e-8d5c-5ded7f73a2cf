"""
基础模型类，定义了所有深度学习模型的通用接口和方法
"""

import os
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import joblib
from typing import Dict, List, Optional, Tuple, Union, Any
from abc import ABC, abstractmethod
import sys
from tqdm import tqdm

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.utils.config import DATA_PATHS, MODEL_CONFIG

class BaseTimeSeriesModel(nn.Module, ABC):
    """
    时间序列预测的基础模型类
    """

    def __init__(self,
                 model_name: str,
                 sequence_length: int,
                 n_features: Optional[int] = None):
        """
        初始化基础模型

        Args:
            model_name: 模型名称
            sequence_length: 时间序列长度
            n_features: 特征数量
        """
        super().__init__()
        self.model_name = model_name
        self.sequence_length = sequence_length
        self.n_features = n_features
        self.device = MODEL_CONFIG['device']
        self.history = {'train_loss': [], 'val_loss': [], 'train_mae': [], 'val_mae': []}
        self.scaler_X = None
        self.scaler_y = None
        self.best_val_loss = float('inf')
        self.early_stop_counter = 0
        self.best_model_state = None

    @abstractmethod
    def forward(self, x):
        """
        前向传播，需要在子类中实现

        Args:
            x: 输入数据

        Returns:
            模型输出
        """
        pass

    def train_model(self,
                  train_loader: DataLoader,
                  val_loader: DataLoader,
                  epochs: int = 100,
                  patience: int = 15,
                  learning_rate: float = 0.001,
                  save_best: bool = True,
                  verbose: int = 1) -> Dict:
        """
        训练模型

        Args:
            train_loader: 训练数据加载器
            val_loader: 验证数据加载器
            epochs: 训练轮数
            patience: 早停耐心值
            learning_rate: 学习率
            save_best: 是否保存最佳模型
            verbose: 显示详细程度

        Returns:
            训练历史
        """
        print(f"开始训练{self.model_name}模型...")

        # 将模型移至指定设备
        self.to(self.device)

        # 定义优化器和损失函数
        optimizer = optim.Adam(self.parameters(), lr=learning_rate)
        criterion = nn.MSELoss()
        mae_criterion = nn.L1Loss()

        # 学习率调度器
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer,
            mode='min',
            factor=0.5,
            patience=patience // 2,
            min_lr=1e-7
        )

        # 训练循环
        for epoch in range(epochs):
            # 训练阶段
            self.train()
            train_loss = 0.0
            train_mae = 0.0
            train_batches = 0

            train_progress = tqdm(train_loader, desc=f'Epoch {epoch+1}/{epochs} [Train]') if verbose > 0 else train_loader
            for inputs, targets in train_progress:
                # 将数据移至设备
                inputs = inputs.to(self.device)
                targets = targets.to(self.device)

                # 梯度清零
                optimizer.zero_grad()

                # 前向传播
                outputs = self(inputs)

                # 计算损失
                loss = criterion(outputs, targets)
                mae = mae_criterion(outputs, targets)

                # 反向传播和优化
                loss.backward()
                optimizer.step()

                # 累计损失
                train_loss += loss.item()
                train_mae += mae.item()
                train_batches += 1

            # 计算平均损失
            avg_train_loss = train_loss / train_batches
            avg_train_mae = train_mae / train_batches

            # 验证阶段
            self.eval()
            val_loss = 0.0
            val_mae = 0.0
            val_batches = 0

            with torch.no_grad():
                val_progress = tqdm(val_loader, desc=f'Epoch {epoch+1}/{epochs} [Val]') if verbose > 0 else val_loader
                for inputs, targets in val_progress:
                    # 将数据移至设备
                    inputs = inputs.to(self.device)
                    targets = targets.to(self.device)

                    # 前向传播
                    outputs = self(inputs)

                    # 计算损失
                    loss = criterion(outputs, targets)
                    mae = mae_criterion(outputs, targets)

                    # 累计损失
                    val_loss += loss.item()
                    val_mae += mae.item()
                    val_batches += 1

            # 计算平均损失
            avg_val_loss = val_loss / val_batches
            avg_val_mae = val_mae / val_batches

            # 更新学习率
            scheduler.step(avg_val_loss)

            # 记录历史
            self.history['train_loss'].append(avg_train_loss)
            self.history['val_loss'].append(avg_val_loss)
            self.history['train_mae'].append(avg_train_mae)
            self.history['val_mae'].append(avg_val_mae)

            # 打印进度
            if verbose > 0:
                print(f"Epoch {epoch+1}/{epochs} - "
                      f"loss: {avg_train_loss:.4f} - "
                      f"mae: {avg_train_mae:.4f} - "
                      f"val_loss: {avg_val_loss:.4f} - "
                      f"val_mae: {avg_val_mae:.4f} - "
                      f"lr: {optimizer.param_groups[0]['lr']:.6f}")

            # 保存最佳模型
            if avg_val_loss < self.best_val_loss:
                self.best_val_loss = avg_val_loss
                self.early_stop_counter = 0

                if save_best:
                    self.best_model_state = self.state_dict().copy()

                    if verbose > 0:
                        print(f"Epoch {epoch+1}: val_loss improved to {self.best_val_loss:.6f}")
            else:
                self.early_stop_counter += 1
                if verbose > 0:
                    print(f"Epoch {epoch+1}: val_loss did not improve from {self.best_val_loss:.6f}")

                # 早停
                if self.early_stop_counter >= patience:
                    if verbose > 0:
                        print(f"Early stopping triggered after {epoch+1} epochs")
                    break

        # 恢复最佳模型
        if save_best and self.best_model_state is not None:
            self.load_state_dict(self.best_model_state)
            print(f"恢复最佳模型权重")

        print(f"{self.model_name}模型训练完成！")
        return self.history
    
    def predict(self, X: np.ndarray, denormalize: bool = True) -> np.ndarray:
        """
        模型预测

        Args:
            X: 输入特征
            denormalize: 是否反标准化预测结果

        Returns:
            预测结果
        """
        self.eval()

        # 转换为PyTorch张量
        X_tensor = torch.FloatTensor(X).to(self.device)

        with torch.no_grad():
            predictions_scaled = self(X_tensor).cpu().numpy()

        if denormalize and self.scaler_y is not None:
            predictions = self.scaler_y.inverse_transform(predictions_scaled.reshape(-1, 1))
            return predictions.flatten()

        return predictions_scaled.flatten()
    
    def evaluate(self,
                X_train: np.ndarray,
                y_train: np.ndarray,
                X_val: np.ndarray,
                y_val: np.ndarray,
                X_test: np.ndarray,
                y_test: np.ndarray,
                use_normalized_metrics: bool = True) -> Dict:
        """
        评估模型性能

        Args:
            X_train: 训练特征
            y_train: 训练标签
            X_val: 验证特征
            y_val: 验证标签
            X_test: 测试特征
            y_test: 测试标签
            use_normalized_metrics: 是否使用标准化后的指标（默认True，显示零点几的数值）

        Returns:
            包含评估结果的字典
        """
        from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score

        print(f"评估{self.model_name}模型性能...")

        if use_normalized_metrics:
            print("使用标准化后的指标（0-1范围内的数值）")
            # 使用标准化后的预测和真实值
            y_train_pred = self.predict(X_train, denormalize=False)
            y_val_pred = self.predict(X_val, denormalize=False)
            y_test_pred = self.predict(X_test, denormalize=False)

            y_train_true = y_train
            y_val_true = y_val
            y_test_true = y_test
        else:
            print("使用原始功率值的指标")
            # 使用反标准化后的预测和真实值
            y_train_pred = self.predict(X_train, denormalize=True)
            y_val_pred = self.predict(X_val, denormalize=True)
            y_test_pred = self.predict(X_test, denormalize=True)

            # 反标准化真实值
            if self.scaler_y is not None:
                y_train_true = self.scaler_y.inverse_transform(y_train.reshape(-1, 1)).flatten()
                y_val_true = self.scaler_y.inverse_transform(y_val.reshape(-1, 1)).flatten()
                y_test_true = self.scaler_y.inverse_transform(y_test.reshape(-1, 1)).flatten()
            else:
                y_train_true = y_train
                y_val_true = y_val
                y_test_true = y_test

        # 计算指标
        def calculate_metrics(y_true, y_pred, dataset_name):
            mae = mean_absolute_error(y_true, y_pred)
            mse = mean_squared_error(y_true, y_pred)
            rmse = np.sqrt(mse)
            r2 = r2_score(y_true, y_pred)

            # 计算MAPE (Mean Absolute Percentage Error)
            # 避免除以零
            mask = y_true != 0
            mape = np.mean(np.abs((y_true[mask] - y_pred[mask]) / y_true[mask])) * 100

            print(f"\n{dataset_name}集性能:")
            print(f"MAE: {mae:.4f}")
            print(f"MSE: {mse:.4f}")
            print(f"RMSE: {rmse:.4f}")
            print(f"R²: {r2:.4f}")
            print(f"MAPE: {mape:.4f}%")

            return {'MAE': mae, 'MSE': mse, 'RMSE': rmse, 'R2': r2, 'MAPE': mape}

        train_metrics = calculate_metrics(y_train_true, y_train_pred, "训练")
        val_metrics = calculate_metrics(y_val_true, y_val_pred, "验证")
        test_metrics = calculate_metrics(y_test_true, y_test_pred, "测试")

        return {
            'train': train_metrics,
            'val': val_metrics,
            'test': test_metrics,
            'predictions': {
                'train': (y_train_true, y_train_pred),
                'val': (y_val_true, y_val_pred),
                'test': (y_test_true, y_test_pred)
            }
        }
    
    def save_model(self,
                  model_path: Optional[str] = None,
                  scaler_path: Optional[str] = None) -> Tuple[str, str]:
        """
        保存模型和标准化器

        Args:
            model_path: 模型保存路径，如果为None则使用默认路径
            scaler_path: 标准化器保存路径，如果为None则使用默认路径

        Returns:
            model_path: 模型保存路径
            scaler_path: 标准化器保存路径
        """
        # 创建模型目录
        model_dir = os.path.join(DATA_PATHS['models'], self.model_name.lower())
        os.makedirs(model_dir, exist_ok=True)

        # 保存模型
        if model_path is None:
            model_path = os.path.join(model_dir, f'{self.model_name.lower()}_model.pth')

        torch.save(self.state_dict(), model_path)
        print(f"模型已保存到: {model_path}")

        # 保存标准化器和配置
        if scaler_path is None:
            scaler_path = os.path.join(model_dir, f'{self.model_name.lower()}_scalers.pkl')

        joblib.dump({
            'scaler_X': self.scaler_X,
            'scaler_y': self.scaler_y,
            'sequence_length': self.sequence_length,
            'n_features': self.n_features,
            'model_name': self.model_name
        }, scaler_path)
        print(f"标准化器和配置已保存到: {scaler_path}")

        return model_path, scaler_path

    def load_model(self,
                  model_path: Optional[str] = None,
                  scaler_path: Optional[str] = None) -> None:
        """
        加载模型和标准化器

        Args:
            model_path: 模型加载路径，如果为None则使用默认路径
            scaler_path: 标准化器加载路径，如果为None则使用默认路径
        """
        # 确定模型目录
        model_dir = os.path.join(DATA_PATHS['models'], self.model_name.lower())

        # 加载模型
        if model_path is None:
            model_path = os.path.join(model_dir, f'{self.model_name.lower()}_model.pth')

        self.load_state_dict(torch.load(model_path, map_location=self.device))
        print(f"模型已从 {model_path} 加载")

        # 加载标准化器和配置
        if scaler_path is None:
            scaler_path = os.path.join(model_dir, f'{self.model_name.lower()}_scalers.pkl')

        scalers = joblib.load(scaler_path)
        self.scaler_X = scalers['scaler_X']
        self.scaler_y = scalers['scaler_y']
        self.sequence_length = scalers['sequence_length']
        self.n_features = scalers['n_features']
        print(f"标准化器和配置已从 {scaler_path} 加载")

    def set_scalers(self, scaler_X, scaler_y) -> None:
        """
        设置标准化器

        Args:
            scaler_X: 特征标准化器
            scaler_y: 目标变量标准化器
        """
        self.scaler_X = scaler_X
        self.scaler_y = scaler_y
