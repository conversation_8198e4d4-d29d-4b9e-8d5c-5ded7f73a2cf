"""
DPTAM与BiGRU串行融合模型，用于风电功率预测
结合DPTAM的时序注意力机制和BiGRU的双向特征提取能力
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import List, Optional, Dict, Any
import os
import sys

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.models.base_model import BaseTimeSeriesModel
from src.utils.config import DATA_PATHS, DPTAM_BIGRU_CONFIG

# 改进的DPTAM模块，适配风电数据
class AdaptedDPTAM(nn.Module):
    """
    适配风电功率预测的DPTAM模块
    """
    def __init__(self,
                 in_channels,
                 n_segment,
                 kernel_size=3,
                 stride=1,
                 padding=1):
        super(AdaptedDPTAM, self).__init__()
        self.in_channels = in_channels
        self.n_segment = n_segment
        self.kernel_size = kernel_size
        self.stride = stride
        self.padding = padding
        
        print(f'AdaptedDPTAM初始化: channels={in_channels}, segments={n_segment}, kernel_size={kernel_size}')

        # 上下文建模层（保留但不在主流程中使用）
        self.conv_mask = nn.Conv2d(in_channels, 1, kernel_size=3, padding=1)
        self.softmax = nn.Softmax(dim=2)
        
        # 通道交互层
        self.channel_conv = nn.Conv1d(in_channels, in_channels, 1, bias=False)
        
        # DPTAM核心：时序注意力机制
        self.dptam = nn.Sequential(
            nn.Conv1d(in_channels,
                      in_channels // 4,
                      kernel_size,
                      stride=1,
                      padding=kernel_size // 2,
                      bias=False), 
            nn.BatchNorm1d(in_channels // 4),
            nn.ReLU(inplace=True),
            nn.Conv1d(in_channels // 4, in_channels, 1, bias=False),
            nn.Sigmoid()
        )

    def forward(self, x):
        """
        前向传播
        
        Args:
            x: 输入数据，形状为 (nt, c, h, w) 其中 h 是时间维度
        
        Returns:
            带注意力权重的输出数据
        """
        nt, c, h, w = x.size()
        
        t = self.n_segment
        n_batch = nt // t
        
        # 重塑数据: (n_batch, t, c, h, w) -> (n_batch, c, t, h, w)
        new_x = x.view(n_batch, t, c, h, w).permute(0, 2, 1, 3, 4).contiguous()
        
        # 自适应平均池化到 (1, 1) 空间维度
        out = F.adaptive_avg_pool2d(new_x.view(n_batch * c, t, h, w), (1, 1))
        
        # 重塑为 (n_batch, c, t) 用于1D卷积处理
        x_temporal = out.view(n_batch, c, t)
        
        # 通道交互处理
        x_channel_interact = self.channel_conv(x_temporal)
        
        # 时序平均池化
        x_temporal_avg = x_temporal.mean(2, keepdim=True)
        x_temporal_avg = self.channel_conv(x_temporal_avg)
        
        # 特征融合
        x_fused = x_channel_interact * x_temporal_avg
        x_fused = x_temporal + x_fused
        
        # 生成时序注意力权重
        local_activation = self.dptam(x_fused).view(n_batch, c, t, 1, 1)
        
        # 应用注意力权重
        new_x = new_x * local_activation
        
        # 恢复原始维度顺序
        out = new_x.view(n_batch, c, t, h, w)
        out = out.permute(0, 2, 1, 3, 4).contiguous().view(nt, c, h, w)
        
        return out


# 注意：DPTAM_BIGRU_CONFIG 现在从 src.utils.config 导入


class DPTAMBiGRUModel(BaseTimeSeriesModel):
    """DPTAM与BiGRU串行融合模型类，用于风电功率预测"""

    def __init__(self,
                 sequence_length: int,
                 n_features: Optional[int] = None,
                 n_segment: Optional[int] = None,
                 dptam_kernel_size: int = 3,
                 bigru_units: Optional[List[int]] = None,
                 dense_units: Optional[List[int]] = None,
                 dropout_rate: float = 0.2):
        """
        初始化DPTAM-BiGRU融合模型

        Args:
            sequence_length: 时间序列长度
            n_features: 特征数量
            n_segment: DPTAM分段数
            dptam_kernel_size: DPTAM卷积核大小
            bigru_units: 双向GRU层的单元数列表
            dense_units: 全连接层的单元数列表
            dropout_rate: Dropout比例
        """
        super().__init__('DPTAM-BiGRU', sequence_length, n_features)

        # 使用默认配置或自定义配置
        self.n_segment = n_segment if n_segment is not None else DPTAM_BIGRU_CONFIG['n_segment']
        self.dptam_kernel_size = dptam_kernel_size
        self.bigru_units = bigru_units if bigru_units is not None else DPTAM_BIGRU_CONFIG['bigru_units']
        self.dense_units = dense_units if dense_units is not None else DPTAM_BIGRU_CONFIG['dense_units']
        self.dropout_rate = dropout_rate
        
        # 验证分段配置
        if sequence_length % self.n_segment != 0:
            raise ValueError(f"序列长度{sequence_length}必须能被分段数{self.n_segment}整除")
        
        self.segment_length = sequence_length // self.n_segment
        
        print(f"DPTAM-BiGRU模型配置:")
        print(f"  序列长度: {sequence_length}")
        print(f"  分段数: {self.n_segment}")
        print(f"  每段长度: {self.segment_length}")
        print(f"  特征数: {n_features}")

        # 构建网络层
        if self.n_features is not None:
            self._build_layers()

    def _build_layers(self):
        """构建网络层"""
        print("构建DPTAM-BiGRU融合模型...")

        if self.n_features is None:
            raise ValueError("特征数量未设置，请在初始化时提供n_features参数")

        # 1. DPTAM时序注意力模块
        self.dptam = AdaptedDPTAM(
            in_channels=self.n_features,
            n_segment=self.n_segment,
            kernel_size=self.dptam_kernel_size
        )

        # 2. 双向GRU层
        self.bigru_layers = nn.ModuleList()
        self.batch_norms = nn.ModuleList()
        self.dropouts = nn.ModuleList()

        # 第一个双向GRU层
        self.bigru_layers.append(nn.GRU(
            input_size=self.n_features,
            hidden_size=self.bigru_units[0],
            batch_first=True,
            bidirectional=True,  # 双向
            dropout=self.dropout_rate if len(self.bigru_units) > 1 else 0
        ))

        # 批归一化和Dropout（双向输出的维度是hidden_size * 2）
        self.batch_norms.append(nn.BatchNorm1d(self.bigru_units[0] * 2))
        self.dropouts.append(nn.Dropout(self.dropout_rate))

        # 后续双向GRU层
        for i in range(1, len(self.bigru_units)):
            self.bigru_layers.append(nn.GRU(
                input_size=self.bigru_units[i-1] * 2,  # 前一层双向输出
                hidden_size=self.bigru_units[i],
                batch_first=True,
                bidirectional=True,
                dropout=self.dropout_rate if i < len(self.bigru_units) - 1 else 0
            ))

            self.batch_norms.append(nn.BatchNorm1d(self.bigru_units[i] * 2))
            self.dropouts.append(nn.Dropout(self.dropout_rate))

        # 3. 全连接层
        self.dense_layers = nn.ModuleList()
        self.dense_dropouts = nn.ModuleList()

        # 第一个全连接层的输入维度是最后一个双向GRU层的输出维度
        input_dim = self.bigru_units[-1] * 2

        for units in self.dense_units:
            self.dense_layers.append(nn.Linear(input_dim, units))
            self.dense_dropouts.append(nn.Dropout(self.dropout_rate))
            input_dim = units

        # 4. 输出层
        self.output_layer = nn.Linear(input_dim, 1)

        print(f"DPTAM-BiGRU融合模型构建完成:")
        print(f"  DPTAM分段: {self.n_segment}")
        print(f"  双向GRU层: {self.bigru_units}")
        print(f"  全连接层: {self.dense_units}")
        print(f"  Dropout率: {self.dropout_rate}")

    def adapt_input_for_dptam(self, x):
        """
        将风电时序数据适配为DPTAM输入格式

        Args:
            x: 输入数据 (batch_size, sequence_length, n_features)
               例如: (32, 24, 25) - 32个样本，24个时间步，25个特征

        Returns:
            adapted_x: DPTAM格式 (nt, c, h, w)
                      例如: (128, 25, 6, 1) - 128=32*4个时间段，25个通道，6个时间步，1个空间维度
        """
        batch_size, seq_len, n_features = x.shape  # (32, 24, 25)

        # 确保序列长度能被分段数整除
        assert seq_len % self.n_segment == 0, f"序列长度{seq_len}必须能被分段数{self.n_segment}整除"

        segment_length = seq_len // self.n_segment  # 24 // 4 = 6

        # 重塑为分段格式: (batch, n_segment, segment_length, n_features)
        x_segmented = x.view(batch_size, self.n_segment, segment_length, n_features)
        # (32, 4, 6, 25)

        # 调整维度顺序以适配DPTAM: (batch, n_features, n_segment, segment_length)
        x_reordered = x_segmented.permute(0, 3, 1, 2).contiguous()
        # (32, 25, 4, 6)

        # 重塑为DPTAM期望的格式: (batch*n_segment, n_features, segment_length, 1)
        nt = batch_size * self.n_segment  # 32 * 4 = 128
        x_adapted = x_reordered.view(nt, n_features, segment_length, 1)
        # (128, 25, 6, 1)

        return x_adapted

    def restore_from_dptam(self, x_attended, original_batch_size):
        """
        将DPTAM输出恢复为时序格式

        Args:
            x_attended: DPTAM输出 (nt, c, h, w)
            original_batch_size: 原始批次大小

        Returns:
            x_restored: 恢复的时序数据 (batch_size, sequence_length, n_features)
        """
        nt, n_features, segment_length, _ = x_attended.shape

        # 重塑回分段格式: (batch, n_features, n_segment, segment_length)
        x_segments = x_attended.view(original_batch_size, n_features, self.n_segment, segment_length)
        # (32, 25, 4, 6)

        # 调整维度顺序: (batch, n_segment, segment_length, n_features)
        x_reordered = x_segments.permute(0, 2, 3, 1).contiguous()
        # (32, 4, 6, 25)

        # 恢复时序格式: (batch, sequence_length, n_features)
        seq_len = self.n_segment * segment_length
        x_restored = x_reordered.view(original_batch_size, seq_len, n_features)
        # (32, 24, 25)

        return x_restored

    def apply_dptam_attention(self, x):
        """
        应用DPTAM时序注意力机制

        处理流程:
        1. 输入适配: (B,24,25) → (B*4,25,6,1)
        2. DPTAM处理: 生成时序注意力权重
        3. 注意力应用: 原始特征 × 注意力权重
        4. 输出恢复: (B*4,25,6,1) → (B,24,25)
        """
        batch_size, seq_len, n_features = x.shape

        # 1. 维度适配
        x_adapted = self.adapt_input_for_dptam(x)  # (128, 25, 6, 1)

        # 2. DPTAM前向传播
        x_attended = self.dptam(x_adapted)  # (128, 25, 6, 1) 带注意力权重

        # 3. 恢复原始维度结构
        x_output = self.restore_from_dptam(x_attended, batch_size)  # (32, 24, 25)

        return x_output

    def apply_bigru_processing(self, x):
        """
        应用BiGRU进行双向时序特征提取

        Args:
            x: DPTAM处理后的数据 (batch_size, sequence_length, n_features)

        Returns:
            final_features: 最终特征表示 (batch_size, hidden_size*2)
        """
        current_input = x

        # 逐层处理BiGRU
        for i, (bigru_layer, batch_norm, dropout) in enumerate(
            zip(self.bigru_layers, self.batch_norms, self.dropouts)
        ):
            # BiGRU前向传播
            bigru_output, hidden_state = bigru_layer(current_input)
            # bigru_output: (batch, seq_len, hidden_size*2) 因为bidirectional=True

            # 如果不是最后一层，应用批归一化和dropout
            if i < len(self.bigru_layers) - 1:
                # 批归一化需要调整维度: (batch, seq, hidden) → (batch, hidden, seq)
                bigru_output = bigru_output.transpose(1, 2)
                bigru_output = batch_norm(bigru_output)
                bigru_output = bigru_output.transpose(1, 2)

                # 应用dropout
                bigru_output = dropout(bigru_output)

            current_input = bigru_output

        # 提取最后时间步的特征作为序列表示
        final_features = current_input[:, -1, :]  # (batch_size, hidden_size*2)

        return final_features

    def forward(self, x):
        """
        串行融合架构的完整前向传播

        Args:
            x: 输入风电数据 (batch_size, sequence_length, n_features)

        Returns:
            prediction: 功率预测值 (batch_size, 1)
        """
        # 确保输入数据在正确的设备上
        device = next(self.parameters()).device
        if x.device != device:
            x = x.to(device)

        # 阶段1: DPTAM时序注意力处理
        # 目的: 为不同时间段分配注意力权重，突出重要的时序模式
        x_attended = self.apply_dptam_attention(x)

        # 阶段2: BiGRU双向特征提取
        # 目的: 提取长期时序依赖和双向上下文信息
        bigru_features = self.apply_bigru_processing(x_attended)

        # 阶段3: 全连接层处理
        # 目的: 将高维特征映射到预测值
        current_features = bigru_features

        for dense_layer, dropout in zip(self.dense_layers, self.dense_dropouts):
            current_features = torch.relu(dense_layer(current_features))
            current_features = dropout(current_features)

        # 阶段4: 输出预测
        prediction = self.output_layer(current_features)  # (batch_size, 1)

        return prediction

    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息

        Returns:
            包含模型信息的字典
        """
        total_params = sum(p.numel() for p in self.parameters()) if hasattr(self, 'bigru_layers') else None

        return {
            'model_name': self.model_name,
            'sequence_length': self.sequence_length,
            'n_features': self.n_features,
            'n_segment': self.n_segment,
            'segment_length': self.segment_length,
            'dptam_kernel_size': self.dptam_kernel_size,
            'bigru_units': self.bigru_units,
            'dense_units': self.dense_units,
            'dropout_rate': self.dropout_rate,
            'bidirectional': True,
            'total_params': total_params
        }

    def get_attention_weights(self, x):
        """
        获取DPTAM注意力权重用于可视化分析

        Args:
            x: 输入数据 (batch_size, sequence_length, n_features)

        Returns:
            attention_weights: 注意力权重 (batch_size, n_segment)
        """
        batch_size = x.shape[0]
        device = next(self.parameters()).device  # 获取模型所在设备

        # 确保输入数据在正确的设备上
        if x.device != device:
            x = x.to(device)

        # 适配输入格式
        x_adapted = self.adapt_input_for_dptam(x)

        # 获取DPTAM内部的注意力权重
        with torch.no_grad():
            # 模拟DPTAM的处理过程以获取注意力权重
            nt, c, h, w = x_adapted.size()
            t = self.n_segment
            n_batch = nt // t

            new_x = x_adapted.view(n_batch, t, c, h, w).permute(0, 2, 1, 3, 4).contiguous()
            out = F.adaptive_avg_pool2d(new_x.view(n_batch * c, t, h, w), (1, 1))
            x_temporal = out.view(n_batch, c, t)

            # 通过DPTAM网络获取注意力权重
            x_channel_interact = self.dptam.channel_conv(x_temporal)
            x_temporal_avg = x_temporal.mean(2, keepdim=True)
            x_temporal_avg = self.dptam.channel_conv(x_temporal_avg)
            x_fused = x_channel_interact * x_temporal_avg
            x_fused = x_temporal + x_fused

            # 获取注意力权重 (n_batch, c, t)
            attention_weights = self.dptam.dptam(x_fused)

            # 平均所有通道的注意力权重 (n_batch, t)
            attention_weights = attention_weights.mean(dim=1)

        return attention_weights


# 测试代码
if __name__ == "__main__":
    print("=" * 60)
    print("DPTAM-BiGRU融合模型测试")
    print("=" * 60)

    # 创建测试数据
    sequence_length = 24
    n_features = 25
    batch_size = 32
    n_segment = 4

    print(f"测试配置:")
    print(f"  批次大小: {batch_size}")
    print(f"  序列长度: {sequence_length}")
    print(f"  特征数量: {n_features}")
    print(f"  分段数: {n_segment}")

    # 创建DPTAM-BiGRU融合模型
    try:
        model = DPTAMBiGRUModel(
            sequence_length=sequence_length,
            n_features=n_features,
            n_segment=n_segment,
            dptam_kernel_size=3,
            bigru_units=[128, 64],
            dense_units=[64, 32],
            dropout_rate=0.2
        )

        # 获取模型信息
        model_info = model.get_model_info()
        print(f"\n模型信息:")
        for key, value in model_info.items():
            print(f"  {key}: {value}")

        # 测试前向传播
        print(f"\n开始前向传播测试...")
        test_input = torch.randn(batch_size, sequence_length, n_features)
        print(f"输入形状: {test_input.shape}")

        # 设置为评估模式
        model.eval()

        with torch.no_grad():
            # 前向传播
            output = model(test_input)
            print(f"输出形状: {output.shape}")
            print(f"输出范围: [{output.min().item():.4f}, {output.max().item():.4f}]")

            # 测试注意力权重获取
            attention_weights = model.get_attention_weights(test_input)
            print(f"注意力权重形状: {attention_weights.shape}")
            print(f"注意力权重示例 (第一个样本): {attention_weights[0].cpu().numpy()}")

            # 测试各个组件
            print(f"\n组件测试:")

            # 1. 测试DPTAM适配
            x_adapted = model.adapt_input_for_dptam(test_input)
            print(f"  DPTAM适配后形状: {x_adapted.shape}")

            # 2. 测试DPTAM注意力
            x_attended = model.apply_dptam_attention(test_input)
            print(f"  DPTAM注意力后形状: {x_attended.shape}")

            # 3. 测试BiGRU处理
            bigru_features = model.apply_bigru_processing(x_attended)
            print(f"  BiGRU特征形状: {bigru_features.shape}")

        print(f"\n✅ DPTAM-BiGRU融合模型测试成功完成！")
        print(f"模型总参数量: {model_info['total_params']:,}")

    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
