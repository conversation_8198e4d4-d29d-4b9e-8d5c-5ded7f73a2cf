"""
LSTM模型实现，用于风电功率预测
"""

import torch
import torch.nn as nn
from typing import List, Optional, Dict, Any
import sys
import os

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.models.base_model import BaseTimeSeriesModel
from src.utils.config import LSTM_CONFIG

class LSTMModel(BaseTimeSeriesModel):
    """LSTM模型类，用于风电功率预测"""

    def __init__(self,
                 sequence_length: int,
                 n_features: Optional[int] = None,
                 lstm_units: Optional[List[int]] = None,
                 dense_units: Optional[List[int]] = None,
                 dropout_rate: float = 0.2):
        """
        初始化LSTM模型

        Args:
            sequence_length: 时间序列长度
            n_features: 特征数量
            lstm_units: LSTM层的单元数列表
            dense_units: 全连接层的单元数列表
            dropout_rate: Dropout比例
        """
        super().__init__('LSTM', sequence_length, n_features)

        # 使用默认配置或自定义配置
        self.lstm_units = lstm_units if lstm_units is not None else LSTM_CONFIG['lstm_units']
        self.dense_units = dense_units if dense_units is not None else LSTM_CONFIG['dense_units']
        self.dropout_rate = dropout_rate

        # 构建网络层
        if self.n_features is not None:
            self._build_layers()

    def _build_layers(self):
        """构建网络层"""
        print("构建LSTM模型...")

        if self.n_features is None:
            raise ValueError("特征数量未设置，请在初始化时提供n_features参数")

        # LSTM层
        self.lstm_layers = nn.ModuleList()
        self.batch_norms = nn.ModuleList()
        self.dropouts = nn.ModuleList()

        # 第一个LSTM层
        self.lstm_layers.append(nn.LSTM(
            input_size=self.n_features,
            hidden_size=self.lstm_units[0],
            batch_first=True,
            dropout=self.dropout_rate if len(self.lstm_units) > 1 else 0
        ))
        self.batch_norms.append(nn.BatchNorm1d(self.lstm_units[0]))
        self.dropouts.append(nn.Dropout(self.dropout_rate))

        # 额外的LSTM层
        for i in range(1, len(self.lstm_units)):
            self.lstm_layers.append(nn.LSTM(
                input_size=self.lstm_units[i-1],
                hidden_size=self.lstm_units[i],
                batch_first=True,
                dropout=self.dropout_rate if i < len(self.lstm_units) - 1 else 0
            ))
            self.batch_norms.append(nn.BatchNorm1d(self.lstm_units[i]))
            self.dropouts.append(nn.Dropout(self.dropout_rate))

        # 全连接层
        self.dense_layers = nn.ModuleList()
        self.dense_dropouts = nn.ModuleList()

        # 输入维度是最后一个LSTM层的输出维度
        input_dim = self.lstm_units[-1]

        for units in self.dense_units:
            self.dense_layers.append(nn.Linear(input_dim, units))
            self.dense_dropouts.append(nn.Dropout(self.dropout_rate))
            input_dim = units

        # 输出层
        self.output_layer = nn.Linear(input_dim, 1)

        print("LSTM模型架构:")
        print(self)

        # 计算参数数量
        total_params = sum(p.numel() for p in self.parameters())
        print(f"总参数量: {total_params:,}")

    def forward(self, x):
        """
        前向传播

        Args:
            x: 输入数据，形状为 (batch_size, sequence_length, n_features)

        Returns:
            输出预测值，形状为 (batch_size, 1)
        """
        # LSTM层
        for i, (lstm_layer, batch_norm, dropout) in enumerate(zip(self.lstm_layers, self.batch_norms, self.dropouts)):
            if i == 0:
                # 第一层直接使用输入
                lstm_out, _ = lstm_layer(x)
            else:
                # 后续层使用前一层的输出
                lstm_out, _ = lstm_layer(lstm_out)

            # 如果不是最后一个LSTM层，应用批归一化和dropout
            if i < len(self.lstm_layers) - 1:
                # 批归一化需要调整维度: (batch, seq, hidden) -> (batch, hidden, seq) -> (batch, seq, hidden)
                lstm_out = lstm_out.transpose(1, 2)
                lstm_out = batch_norm(lstm_out)
                lstm_out = lstm_out.transpose(1, 2)
                lstm_out = dropout(lstm_out)

        # 取最后一个时间步的输出
        last_output = lstm_out[:, -1, :]  # (batch_size, hidden_size)

        # 全连接层
        x = last_output
        for dense_layer, dropout in zip(self.dense_layers, self.dense_dropouts):
            x = torch.relu(dense_layer(x))
            x = dropout(x)

        # 输出层
        output = self.output_layer(x)

        return output

    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息

        Returns:
            包含模型信息的字典
        """
        total_params = sum(p.numel() for p in self.parameters()) if hasattr(self, 'lstm_layers') else None

        return {
            'model_name': self.model_name,
            'sequence_length': self.sequence_length,
            'n_features': self.n_features,
            'lstm_units': self.lstm_units,
            'dense_units': self.dense_units,
            'dropout_rate': self.dropout_rate,
            'total_params': total_params
        }

# 测试代码
if __name__ == "__main__":
    import numpy as np
    from torch.utils.data import DataLoader, TensorDataset

    # 创建一些测试数据
    sequence_length = 24
    n_features = 25
    n_samples = 1000

    X_train = np.random.random((n_samples, sequence_length, n_features)).astype(np.float32)
    y_train = np.random.random((n_samples, 1)).astype(np.float32)
    X_val = np.random.random((n_samples//5, sequence_length, n_features)).astype(np.float32)
    y_val = np.random.random((n_samples//5, 1)).astype(np.float32)

    # 创建数据加载器
    train_dataset = TensorDataset(torch.FloatTensor(X_train), torch.FloatTensor(y_train))
    val_dataset = TensorDataset(torch.FloatTensor(X_val), torch.FloatTensor(y_val))
    train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=32, shuffle=False)

    # 创建并训练LSTM模型
    lstm_model = LSTMModel(sequence_length=sequence_length, n_features=n_features)

    # 训练模型（仅用于测试，epochs设置较小）
    history = lstm_model.train_model(
        train_loader=train_loader,
        val_loader=val_loader,
        epochs=5,
        patience=10
    )

    # 获取模型信息
    model_info = lstm_model.get_model_info()
    print("模型信息:", model_info)

    # 测试预测
    test_pred = lstm_model.predict(X_val[:10])
    print(f"预测结果形状: {test_pred.shape}")
    print(f"前5个预测值: {test_pred[:5]}")
