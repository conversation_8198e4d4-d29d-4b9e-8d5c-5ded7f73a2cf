"""
评估指标模块，提供各种模型评估指标的计算
"""

import numpy as np
import pandas as pd
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from typing import Dict, List, Tuple, Optional, Any
import sys
import os

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.utils.config import DATA_PATHS

class ModelEvaluator:
    """模型评估器，提供各种评估指标的计算"""
    
    def __init__(self):
        """初始化评估器"""
        self.metrics_names = ['MAE', 'MSE', 'RMSE', 'R2', 'MAPE', 'SMAPE', 'WAPE']
    
    def calculate_mae(self, y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """计算平均绝对误差 (Mean Absolute Error)"""
        return mean_absolute_error(y_true, y_pred)
    
    def calculate_mse(self, y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """计算均方误差 (Mean Squared Error)"""
        return mean_squared_error(y_true, y_pred)
    
    def calculate_rmse(self, y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """计算均方根误差 (Root Mean Squared Error)"""
        return np.sqrt(mean_squared_error(y_true, y_pred))
    
    def calculate_r2(self, y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """计算决定系数 (R-squared)"""
        return r2_score(y_true, y_pred)
    
    def calculate_mape(self, y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """
        计算平均绝对百分比误差 (Mean Absolute Percentage Error)
        
        Args:
            y_true: 真实值
            y_pred: 预测值
            
        Returns:
            MAPE值 (百分比)
        """
        # 避免除以零
        mask = y_true != 0
        if np.sum(mask) == 0:
            return np.inf
        
        return np.mean(np.abs((y_true[mask] - y_pred[mask]) / y_true[mask])) * 100
    
    def calculate_smape(self, y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """
        计算对称平均绝对百分比误差 (Symmetric Mean Absolute Percentage Error)
        
        Args:
            y_true: 真实值
            y_pred: 预测值
            
        Returns:
            SMAPE值 (百分比)
        """
        denominator = (np.abs(y_true) + np.abs(y_pred)) / 2
        mask = denominator != 0
        if np.sum(mask) == 0:
            return np.inf
        
        return np.mean(np.abs(y_true[mask] - y_pred[mask]) / denominator[mask]) * 100
    
    def calculate_wape(self, y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """
        计算加权平均绝对百分比误差 (Weighted Absolute Percentage Error)
        
        Args:
            y_true: 真实值
            y_pred: 预测值
            
        Returns:
            WAPE值 (百分比)
        """
        if np.sum(np.abs(y_true)) == 0:
            return np.inf
        
        return np.sum(np.abs(y_true - y_pred)) / np.sum(np.abs(y_true)) * 100
    
    def calculate_all_metrics(self, y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, float]:
        """
        计算所有评估指标
        
        Args:
            y_true: 真实值
            y_pred: 预测值
            
        Returns:
            包含所有指标的字典
        """
        metrics = {
            'MAE': self.calculate_mae(y_true, y_pred),
            'MSE': self.calculate_mse(y_true, y_pred),
            'RMSE': self.calculate_rmse(y_true, y_pred),
            'R2': self.calculate_r2(y_true, y_pred),
            'MAPE': self.calculate_mape(y_true, y_pred),
            'SMAPE': self.calculate_smape(y_true, y_pred),
            'WAPE': self.calculate_wape(y_true, y_pred)
        }
        
        return metrics
    
    def evaluate_model_performance(self, 
                                  y_true_dict: Dict[str, np.ndarray],
                                  y_pred_dict: Dict[str, np.ndarray],
                                  model_name: str = "Model") -> Dict[str, Dict[str, float]]:
        """
        评估模型在不同数据集上的性能
        
        Args:
            y_true_dict: 包含不同数据集真实值的字典
            y_pred_dict: 包含不同数据集预测值的字典
            model_name: 模型名称
            
        Returns:
            包含各数据集评估结果的字典
        """
        results = {}
        
        for dataset in y_true_dict.keys():
            if dataset in y_pred_dict:
                y_true = y_true_dict[dataset]
                y_pred = y_pred_dict[dataset]
                
                metrics = self.calculate_all_metrics(y_true, y_pred)
                results[dataset] = metrics
                
                # 打印结果
                print(f"\n{model_name} - {dataset.capitalize()}集性能:")
                for metric_name, value in metrics.items():
                    if metric_name in ['MAPE', 'SMAPE', 'WAPE']:
                        print(f"{metric_name}: {value:.4f}%")
                    else:
                        print(f"{metric_name}: {value:.4f}")
        
        return results
    
    def compare_models(self, 
                      models_results: Dict[str, Dict[str, Dict[str, float]]],
                      save_path: Optional[str] = None) -> pd.DataFrame:
        """
        比较多个模型的性能
        
        Args:
            models_results: 包含多个模型评估结果的字典
            save_path: 保存路径
            
        Returns:
            包含比较结果的DataFrame
        """
        comparison_data = []
        
        for model_name, model_results in models_results.items():
            for dataset, metrics in model_results.items():
                for metric_name, value in metrics.items():
                    comparison_data.append({
                        'Model': model_name,
                        'Dataset': dataset,
                        'Metric': metric_name,
                        'Value': value
                    })
        
        df_comparison = pd.DataFrame(comparison_data)
        
        # 创建透视表
        pivot_tables = {}
        for metric in self.metrics_names:
            if metric in df_comparison['Metric'].values:
                pivot_table = df_comparison[df_comparison['Metric'] == metric].pivot(
                    index='Model', columns='Dataset', values='Value'
                )
                pivot_tables[metric] = pivot_table
                
                print(f"\n{metric} 比较:")
                print(pivot_table.round(4))
        
        # 保存结果
        if save_path:
            comparison_file = os.path.join(save_path, 'model_comparison.csv')
            df_comparison.to_csv(comparison_file, index=False)
            print(f"\n模型比较结果已保存到: {comparison_file}")
            
            # 保存各指标的透视表
            for metric, pivot_table in pivot_tables.items():
                metric_file = os.path.join(save_path, f'{metric}_comparison.csv')
                pivot_table.to_csv(metric_file)
        
        return df_comparison
    
    def find_best_model(self, 
                       models_results: Dict[str, Dict[str, Dict[str, float]]],
                       primary_metric: str = 'RMSE',
                       dataset: str = 'test') -> Tuple[str, float]:
        """
        根据指定指标找到最佳模型
        
        Args:
            models_results: 包含多个模型评估结果的字典
            primary_metric: 主要评估指标
            dataset: 数据集类型
            
        Returns:
            最佳模型名称和对应的指标值
        """
        best_model = None
        best_value = None
        
        # 对于R2，值越大越好；对于其他指标，值越小越好
        is_higher_better = primary_metric == 'R2'
        
        for model_name, model_results in models_results.items():
            if dataset in model_results and primary_metric in model_results[dataset]:
                value = model_results[dataset][primary_metric]
                
                if best_model is None:
                    best_model = model_name
                    best_value = value
                else:
                    if is_higher_better:
                        if value > best_value:
                            best_model = model_name
                            best_value = value
                    else:
                        if value < best_value:
                            best_model = model_name
                            best_value = value
        
        print(f"\n基于{primary_metric}指标在{dataset}集上的最佳模型: {best_model} ({primary_metric}: {best_value:.4f})")
        
        return best_model, best_value
    
    def calculate_improvement(self, 
                            baseline_value: float, 
                            improved_value: float, 
                            metric_name: str) -> float:
        """
        计算改进百分比
        
        Args:
            baseline_value: 基准值
            improved_value: 改进后的值
            metric_name: 指标名称
            
        Returns:
            改进百分比
        """
        if metric_name == 'R2':
            # R2越大越好
            improvement = ((improved_value - baseline_value) / baseline_value) * 100
        else:
            # 其他指标越小越好
            improvement = ((baseline_value - improved_value) / baseline_value) * 100
        
        return improvement

# 测试代码
if __name__ == "__main__":
    # 创建评估器实例
    evaluator = ModelEvaluator()
    
    # 创建一些测试数据
    np.random.seed(42)
    y_true = np.random.random(1000) * 100
    y_pred1 = y_true + np.random.normal(0, 5, 1000)  # 模型1
    y_pred2 = y_true + np.random.normal(0, 8, 1000)  # 模型2
    
    # 计算指标
    metrics1 = evaluator.calculate_all_metrics(y_true, y_pred1)
    metrics2 = evaluator.calculate_all_metrics(y_true, y_pred2)
    
    print("模型1指标:")
    for name, value in metrics1.items():
        print(f"{name}: {value:.4f}")
    
    print("\n模型2指标:")
    for name, value in metrics2.items():
        print(f"{name}: {value:.4f}")
    
    # 比较模型
    models_results = {
        'Model1': {'test': metrics1},
        'Model2': {'test': metrics2}
    }
    
    comparison_df = evaluator.compare_models(models_results)
    best_model, best_value = evaluator.find_best_model(models_results)
