#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据格式转换器
自动检查数据文件格式，如果不是CSV格式则转换为CSV并保存在源文件夹中
"""

import os
import pandas as pd
from typing import Optional, Tuple
import warnings
warnings.filterwarnings('ignore')


class DataFormatConverter:
    """数据格式转换器类"""
    
    def __init__(self):
        """初始化转换器"""
        self.supported_formats = {
            '.xlsx': self._read_excel,
            '.xls': self._read_excel,
            '.csv': self._read_csv,
            '.json': self._read_json,
            '.parquet': self._read_parquet
        }
    
    def _read_excel(self, file_path: str, **kwargs) -> pd.DataFrame:
        """读取Excel文件"""
        return pd.read_excel(file_path, **kwargs)
    
    def _read_csv(self, file_path: str, **kwargs) -> pd.DataFrame:
        """读取CSV文件，尝试多种编码"""
        encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1', 'cp1252']
        for encoding in encodings:
            try:
                return pd.read_csv(file_path, encoding=encoding, **kwargs)
            except UnicodeDecodeError:
                continue
        raise ValueError(f"无法使用任何编码读取CSV文件: {file_path}")
    
    def _read_json(self, file_path: str, **kwargs) -> pd.DataFrame:
        """读取JSON文件"""
        return pd.read_json(file_path, **kwargs)
    
    def _read_parquet(self, file_path: str, **kwargs) -> pd.DataFrame:
        """读取Parquet文件"""
        return pd.read_parquet(file_path, **kwargs)
    
    def check_and_convert_to_csv(self, file_path: str, force_convert: bool = False) -> Tuple[str, bool]:
        """
        检查文件格式，如果不是CSV则转换为CSV
        
        Args:
            file_path: 原始文件路径
            force_convert: 是否强制转换（即使已经是CSV格式）
            
        Returns:
            tuple: (csv_file_path, was_converted)
                - csv_file_path: CSV文件路径
                - was_converted: 是否进行了转换
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        # 获取文件信息
        file_dir = os.path.dirname(file_path)
        file_name = os.path.basename(file_path)
        file_name_without_ext = os.path.splitext(file_name)[0]
        file_ext = os.path.splitext(file_path)[1].lower()
        
        # 生成CSV文件路径
        csv_file_path = os.path.join(file_dir, f"{file_name_without_ext}.csv")
        
        # 如果已经是CSV格式且不强制转换
        if file_ext == '.csv' and not force_convert:
            print(f"✅ 文件已经是CSV格式: {file_path}")
            return file_path, False
        
        # 检查CSV文件是否已存在且比原文件新
        if os.path.exists(csv_file_path) and not force_convert:
            original_mtime = os.path.getmtime(file_path)
            csv_mtime = os.path.getmtime(csv_file_path)
            
            if csv_mtime >= original_mtime:
                print(f"✅ CSV文件已存在且是最新的: {csv_file_path}")
                return csv_file_path, False
        
        # 进行格式转换
        print(f"🔄 开始转换文件格式: {file_ext} -> .csv")
        print(f"   源文件: {file_path}")
        print(f"   目标文件: {csv_file_path}")
        
        try:
            # 检查文件格式是否支持
            if file_ext not in self.supported_formats:
                raise ValueError(f"不支持的文件格式: {file_ext}")
            
            # 读取原始文件
            read_func = self.supported_formats[file_ext]
            df = read_func(file_path)
            
            print(f"   读取成功，数据形状: {df.shape}")
            print(f"   数据列: {list(df.columns)}")
            
            # 标准化列名（保持与配置一致）
            column_mapping = {
                'Time(year-month-day h:m:s)': 'Time(year-month-day h:m:s)',  # 保持原名
                'Power (MW)': 'Power (MW)'  # 保持原名
            }

            # 应用列名映射（如果需要）
            df = df.rename(columns=column_mapping)

            # 保存为CSV格式
            df.to_csv(csv_file_path, index=False, encoding='utf-8')
            
            print(f"✅ 转换完成: {csv_file_path}")
            print(f"   文件大小: {os.path.getsize(csv_file_path) / 1024 / 1024:.2f} MB")
            
            return csv_file_path, True
            
        except Exception as e:
            print(f"❌ 转换失败: {str(e)}")
            raise
    
    def convert_multiple_files(self, file_paths: list, force_convert: bool = False) -> dict:
        """
        批量转换多个文件
        
        Args:
            file_paths: 文件路径列表
            force_convert: 是否强制转换
            
        Returns:
            dict: 转换结果字典 {原始路径: (CSV路径, 是否转换)}
        """
        results = {}
        
        print(f"📁 开始批量转换 {len(file_paths)} 个文件...")
        
        for i, file_path in enumerate(file_paths, 1):
            print(f"\n[{i}/{len(file_paths)}] 处理文件: {os.path.basename(file_path)}")
            
            try:
                csv_path, was_converted = self.check_and_convert_to_csv(file_path, force_convert)
                results[file_path] = (csv_path, was_converted)
                
            except Exception as e:
                print(f"❌ 处理失败: {str(e)}")
                results[file_path] = (None, False)
        
        # 统计结果
        converted_count = sum(1 for _, (_, was_converted) in results.items() if was_converted)
        success_count = sum(1 for _, (csv_path, _) in results.items() if csv_path is not None)
        
        print(f"\n📊 批量转换完成:")
        print(f"   总文件数: {len(file_paths)}")
        print(f"   成功处理: {success_count}")
        print(f"   实际转换: {converted_count}")
        print(f"   失败数量: {len(file_paths) - success_count}")
        
        return results
    
    def get_file_info(self, file_path: str) -> dict:
        """
        获取文件信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            dict: 文件信息
        """
        if not os.path.exists(file_path):
            return {"exists": False}
        
        file_ext = os.path.splitext(file_path)[1].lower()
        file_size = os.path.getsize(file_path)
        
        info = {
            "exists": True,
            "path": file_path,
            "extension": file_ext,
            "size_bytes": file_size,
            "size_mb": file_size / 1024 / 1024,
            "supported": file_ext in self.supported_formats,
            "is_csv": file_ext == '.csv'
        }
        
        return info


def auto_convert_to_csv(file_path: str, force_convert: bool = False) -> str:
    """
    便捷函数：自动转换文件为CSV格式
    
    Args:
        file_path: 原始文件路径
        force_convert: 是否强制转换
        
    Returns:
        str: CSV文件路径
    """
    converter = DataFormatConverter()
    csv_path, was_converted = converter.check_and_convert_to_csv(file_path, force_convert)
    return csv_path


if __name__ == "__main__":
    # 测试代码
    converter = DataFormatConverter()
    
    # 示例：转换Excel文件
    test_file = "data/raw/Wind farm site 6 (Nominal capacity-96MW).xlsx"
    if os.path.exists(test_file):
        print(f"找到测试文件: {test_file}")
        csv_path, was_converted = converter.check_and_convert_to_csv(test_file)
        print(f"结果: {csv_path}, 是否转换: {was_converted}")
    else:
        print(f"测试文件不存在: {test_file}")
        print("当前目录下的数据文件:")
        for root, dirs, files in os.walk("."):
            for file in files:
                if file.endswith(('.xlsx', '.xls', '.csv')):
                    print(f"  {os.path.join(root, file)}")
