"""
可视化管理器
统一管理所有实验的可视化需求，提供标准化的可视化接口
"""

import os
import sys
from typing import Dict, List, Optional, Tuple, Any, Union

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.utils.universal_visualizer import UniversalVisualizer, create_universal_visualizations
# 传统可视化系统已移除，只使用通用可视化系统
from src.utils.config import get_current_paths

class VisualizationManager:
    """可视化管理器 - 统一管理所有可视化需求"""
    
    def __init__(self):
        """初始化可视化管理器"""
        self.universal_visualizer = UniversalVisualizer()
        # 传统可视化系统已移除
        
    def auto_visualize_experiment(self, 
                                 models_data: Dict[str, Tuple[Dict, Dict]], 
                                 experiment_name: str,
                                 save_path: Optional[str] = None,
                                 include_legacy: bool = True) -> Dict[str, List[str]]:
        """
        自动为实验生成完整的可视化套件
        
        Args:
            models_data: {model_name: (results, history)} 格式的数据
            experiment_name: 实验名称
            save_path: 保存路径
            include_legacy: 是否包含传统的可视化方法
            
        Returns:
            {'universal': [files], 'legacy': [files]} 格式的文件路径字典
        """
        if save_path is None:
            save_path = get_current_paths()['figures']
        
        generated_files = {'universal': [], 'legacy': []}
        
        print(f"\n🎨 开始为实验 '{experiment_name}' 生成完整可视化套件...")
        print(f"📊 检测到模型: {', '.join(models_data.keys())}")
        
        # 1. 使用通用可视化系统
        try:
            universal_files = self.universal_visualizer.quick_visualize(
                models_data=models_data,
                experiment_name=experiment_name,
                save_path=save_path
            )
            generated_files['universal'] = universal_files
            print(f"✅ 通用可视化系统生成了 {len(universal_files)} 个图表")
        except Exception as e:
            print(f"❌ 通用可视化系统出错: {str(e)}")
        
        # 传统可视化系统已移除，只使用通用系统
        
        total_files = len(generated_files['universal']) + len(generated_files['legacy'])
        print(f"\n🎉 实验 '{experiment_name}' 可视化完成！")
        print(f"📁 总共生成 {total_files} 个图表文件")
        
        return generated_files
    
    # 传统可视化方法已移除，只使用通用可视化系统
    
    def visualize_single_model(self, 
                              model_name: str,
                              results: Dict,
                              history: Dict,
                              experiment_name: str,
                              save_path: Optional[str] = None) -> List[str]:
        """
        为单个模型生成详细的可视化分析
        
        Args:
            model_name: 模型名称
            results: 模型结果
            history: 训练历史
            experiment_name: 实验名称
            save_path: 保存路径
            
        Returns:
            生成的图片文件路径列表
        """
        if save_path is None:
            save_path = get_current_paths()['figures']
        
        models_data = {model_name: (results, history)}
        
        return self.auto_visualize_experiment(
            models_data=models_data,
            experiment_name=f"{experiment_name}_{model_name}",
            save_path=save_path,
            include_legacy=False  # 单模型不需要传统对比图
        )['universal']
    
    def compare_models(self, 
                      models_data: Dict[str, Tuple[Dict, Dict]], 
                      experiment_name: str = "Model_Comparison",
                      save_path: Optional[str] = None) -> Dict[str, List[str]]:
        """
        比较多个模型的性能
        
        Args:
            models_data: {model_name: (results, history)} 格式的数据
            experiment_name: 实验名称
            save_path: 保存路径
            
        Returns:
            生成的图片文件路径字典
        """
        return self.auto_visualize_experiment(
            models_data=models_data,
            experiment_name=experiment_name,
            save_path=save_path,
            include_legacy=True
        )

# 全局可视化管理器实例
_global_viz_manager = None

def get_visualization_manager() -> VisualizationManager:
    """获取全局可视化管理器实例"""
    global _global_viz_manager
    if _global_viz_manager is None:
        _global_viz_manager = VisualizationManager()
    return _global_viz_manager

# 便捷函数
def auto_visualize(models_data: Dict[str, Tuple[Dict, Dict]], 
                  experiment_name: str,
                  save_path: Optional[str] = None) -> Dict[str, List[str]]:
    """
    自动生成可视化的便捷函数
    
    Args:
        models_data: {model_name: (results, history)} 格式的数据
        experiment_name: 实验名称
        save_path: 保存路径
        
    Returns:
        生成的图片文件路径字典
        
    Example:
        # 使用示例
        models_data = {
            'BiGRU': (bigru_results, bigru_history),
            'DPTAM-BiGRU': (dptam_results, dptam_history)
        }
        files = auto_visualize(models_data, "BiGRU_vs_DPTAM")
    """
    manager = get_visualization_manager()
    return manager.auto_visualize_experiment(models_data, experiment_name, save_path)

def visualize_single(model_name: str, results: Dict, history: Dict, 
                    experiment_name: str, save_path: Optional[str] = None) -> List[str]:
    """
    单模型可视化的便捷函数
    
    Args:
        model_name: 模型名称
        results: 模型结果
        history: 训练历史
        experiment_name: 实验名称
        save_path: 保存路径
        
    Returns:
        生成的图片文件路径列表
    """
    manager = get_visualization_manager()
    return manager.visualize_single_model(model_name, results, history, experiment_name, save_path)

# 测试代码
if __name__ == "__main__":
    print("🎨 可视化管理器测试")
    print("=" * 50)
    
    # 创建可视化管理器
    manager = VisualizationManager()
    
    print("✅ 可视化管理器初始化完成")
    print("💡 使用 auto_visualize() 函数快速生成所有可视化")
    print("💡 使用 visualize_single() 函数为单个模型生成可视化")
