"""
配置验证器
验证config.py中的配置参数是否正确设置
"""

import os
import sys

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.utils.config import (
    DATA_PATHS, MODEL_CONFIG, GRU_CONFIG, LSTM_CONFIG, 
    BIGRU_CONFIG, DPTAM_BIGRU_CONFIG
)

def validate_data_paths():
    """验证数据路径配置"""
    print("验证数据路径配置...")
    
    required_paths = ['raw_data', 'processed_data', 'models', 'figures', 'reports']
    
    for path_name in required_paths:
        if path_name in DATA_PATHS:
            path = DATA_PATHS[path_name]
            print(f"  ✅ {path_name}: {path}")
            
            # 检查目录是否存在，如果不存在则创建
            if not os.path.exists(path):
                try:
                    os.makedirs(path, exist_ok=True)
                    print(f"    📁 目录已创建: {path}")
                except Exception as e:
                    print(f"    ❌ 无法创建目录: {e}")
        else:
            print(f"  ❌ 缺少路径配置: {path_name}")

def validate_model_config():
    """验证基础模型配置"""
    print("\n验证基础模型配置...")
    
    required_keys = ['sequence_length', 'train_ratio', 'val_ratio', 'test_ratio', 
                    'batch_size', 'epochs', 'patience', 'learning_rate', 'dropout_rate']
    
    for key in required_keys:
        if key in MODEL_CONFIG:
            value = MODEL_CONFIG[key]
            print(f"  ✅ {key}: {value}")
            
            # 验证数值范围
            if key in ['train_ratio', 'val_ratio', 'test_ratio', 'dropout_rate']:
                if not (0 < value < 1):
                    print(f"    ⚠️  警告: {key} 应该在 (0, 1) 范围内")
            elif key in ['sequence_length', 'batch_size', 'epochs', 'patience']:
                if not isinstance(value, int) or value <= 0:
                    print(f"    ⚠️  警告: {key} 应该是正整数")
            elif key == 'learning_rate':
                if not (0 < value < 1):
                    print(f"    ⚠️  警告: {key} 应该在 (0, 1) 范围内")
        else:
            print(f"  ❌ 缺少配置项: {key}")
    
    # 验证比例总和
    ratio_sum = MODEL_CONFIG.get('train_ratio', 0) + MODEL_CONFIG.get('val_ratio', 0) + MODEL_CONFIG.get('test_ratio', 0)
    if abs(ratio_sum - 1.0) > 0.001:
        print(f"  ⚠️  警告: 数据划分比例总和不等于1.0 (当前: {ratio_sum})")

def validate_gru_config():
    """验证GRU模型配置"""
    print("\n验证GRU模型配置...")
    
    required_keys = ['name', 'gru_units', 'dense_units']
    
    for key in required_keys:
        if key in GRU_CONFIG:
            value = GRU_CONFIG[key]
            print(f"  ✅ {key}: {value}")
            
            if key in ['gru_units', 'dense_units']:
                if not isinstance(value, list) or not all(isinstance(x, int) and x > 0 for x in value):
                    print(f"    ⚠️  警告: {key} 应该是正整数列表")
        else:
            print(f"  ❌ 缺少配置项: {key}")

def validate_lstm_config():
    """验证LSTM模型配置"""
    print("\n验证LSTM模型配置...")
    
    required_keys = ['name', 'lstm_units', 'dense_units']
    
    for key in required_keys:
        if key in LSTM_CONFIG:
            value = LSTM_CONFIG[key]
            print(f"  ✅ {key}: {value}")
            
            if key in ['lstm_units', 'dense_units']:
                if not isinstance(value, list) or not all(isinstance(x, int) and x > 0 for x in value):
                    print(f"    ⚠️  警告: {key} 应该是正整数列表")
        else:
            print(f"  ❌ 缺少配置项: {key}")

def validate_bigru_config():
    """验证BiGRU模型配置"""
    print("\n验证BiGRU模型配置...")
    
    required_keys = ['name', 'bigru_units', 'dense_units', 'dropout_rate', 'bidirectional']
    
    for key in required_keys:
        if key in BIGRU_CONFIG:
            value = BIGRU_CONFIG[key]
            print(f"  ✅ {key}: {value}")
            
            if key in ['bigru_units', 'dense_units']:
                if not isinstance(value, list) or not all(isinstance(x, int) and x > 0 for x in value):
                    print(f"    ⚠️  警告: {key} 应该是正整数列表")
            elif key == 'dropout_rate':
                if not (0 <= value < 1):
                    print(f"    ⚠️  警告: {key} 应该在 [0, 1) 范围内")
            elif key == 'bidirectional':
                if not isinstance(value, bool):
                    print(f"    ⚠️  警告: {key} 应该是布尔值")
        else:
            print(f"  ❌ 缺少配置项: {key}")

def validate_dptam_bigru_config():
    """验证DPTAM-BiGRU融合模型配置"""
    print("\n验证DPTAM-BiGRU融合模型配置...")
    
    required_keys = ['name', 'n_segment', 'dptam_kernel_size', 'bigru_units', 
                    'dense_units', 'dropout_rate', 'bidirectional', 'fusion_strategy']
    
    for key in required_keys:
        if key in DPTAM_BIGRU_CONFIG:
            value = DPTAM_BIGRU_CONFIG[key]
            print(f"  ✅ {key}: {value}")
            
            if key in ['bigru_units', 'dense_units']:
                if not isinstance(value, list) or not all(isinstance(x, int) and x > 0 for x in value):
                    print(f"    ⚠️  警告: {key} 应该是正整数列表")
            elif key in ['n_segment', 'dptam_kernel_size']:
                if not isinstance(value, int) or value <= 0:
                    print(f"    ⚠️  警告: {key} 应该是正整数")
            elif key == 'dropout_rate':
                if not (0 <= value < 1):
                    print(f"    ⚠️  警告: {key} 应该在 [0, 1) 范围内")
            elif key == 'bidirectional':
                if not isinstance(value, bool):
                    print(f"    ⚠️  警告: {key} 应该是布尔值")
            elif key == 'fusion_strategy':
                valid_strategies = ['serial', 'parallel', 'multilevel']
                if value not in valid_strategies:
                    print(f"    ⚠️  警告: {key} 应该是 {valid_strategies} 中的一个")
        else:
            print(f"  ❌ 缺少配置项: {key}")
    
    # 验证分段数与序列长度的兼容性
    sequence_length = MODEL_CONFIG.get('sequence_length', 24)
    n_segment = DPTAM_BIGRU_CONFIG.get('n_segment', 4)
    
    if sequence_length % n_segment != 0:
        print(f"  ⚠️  警告: sequence_length ({sequence_length}) 不能被 n_segment ({n_segment}) 整除")
    else:
        segment_length = sequence_length // n_segment
        print(f"  ℹ️  信息: 每段长度为 {segment_length} 个时间步")

def validate_all_configs():
    """验证所有配置"""
    print("=" * 60)
    print("配置验证器 - 验证config.py中的所有配置")
    print("=" * 60)
    
    try:
        validate_data_paths()
        validate_model_config()
        validate_gru_config()
        validate_lstm_config()
        validate_bigru_config()
        validate_dptam_bigru_config()
        
        print("\n" + "=" * 60)
        print("配置验证完成！")
        print("=" * 60)
        
        print("\n📋 配置摘要:")
        print(f"  序列长度: {MODEL_CONFIG.get('sequence_length', 'N/A')}")
        print(f"  批次大小: {MODEL_CONFIG.get('batch_size', 'N/A')}")
        print(f"  学习率: {MODEL_CONFIG.get('learning_rate', 'N/A')}")
        print(f"  训练轮数: {MODEL_CONFIG.get('epochs', 'N/A')}")
        print(f"  DPTAM分段数: {DPTAM_BIGRU_CONFIG.get('n_segment', 'N/A')}")
        print(f"  BiGRU单元数: {BIGRU_CONFIG.get('bigru_units', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 配置验证过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    validate_all_configs()
