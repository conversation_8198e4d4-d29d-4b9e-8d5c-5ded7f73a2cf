"""
双向GRU模型单独训练脚本
"""

import os
import sys
import numpy as np
import pandas as pd
import torch
import warnings
warnings.filterwarnings('ignore')

# 设置随机种子
torch.manual_seed(42)
np.random.seed(42)

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.data_processing.data_loader import DataLoader
from src.data_processing.preprocessor import DataPreprocessor
from src.models.bigru_model import BiGRUModel
from src.utils.visualization_manager import auto_visualize
from src.utils.metrics import ModelEvaluator
from src.utils.config import (
    MODEL_CONFIG, DATASET_CONFIG, setup_matplotlib,
    setup_training_session, get_current_paths
)

class BiGRUTrainer:
    """双向GRU模型训练器"""
    
    def __init__(self):
        """初始化训练器"""
        # 设置时间戳训练会话
        self.training_paths = setup_training_session()
        print(f"🕒 训练会话时间戳: {self.training_paths['timestamp']}")
        print(f"📁 结果保存路径: {self.training_paths['results_root']}")

        self.data_loader = DataLoader()
        # 使用MinMaxScaler对目标变量进行标准化
        self.preprocessor = DataPreprocessor(use_minmax_for_target=True)
        self.visualizer = AcademicVisualizer()
        self.evaluator = ModelEvaluator()

        self.model = None
        self.history = None
        self.results = None

        # 设置matplotlib
        setup_matplotlib()

        print("=" * 60)
        print("✅ 双向GRU模型训练器已初始化")
        print("目标变量标准化方式: MinMaxScaler (0-1范围)")
        print("=" * 60)
    
    def load_and_prepare_data(self) -> dict:
        """
        加载和准备数据
        
        Returns:
            准备好的训练数据
        """
        print("\n" + "=" * 60)
        print("步骤1: 数据加载和预处理")
        print("=" * 60)
        
        # 加载数据
        df = self.data_loader.load_data()
        
        # 显示原始数据的Power统计信息
        print(f"原始Power数据统计:")
        print(f"  最小值: {df['Power'].min():.4f}")
        print(f"  最大值: {df['Power'].max():.4f}")
        print(f"  平均值: {df['Power'].mean():.4f}")
        print(f"  标准差: {df['Power'].std():.4f}")
        
        # 数据分析和可视化
        self.data_loader.analyze_data()
        self.data_loader.visualize_data(save_path=get_current_paths()['figures'])
        
        # 数据预处理
        self.preprocessor.set_data(df)
        df_processed = self.preprocessor.preprocess()
        feature_columns = self.preprocessor.select_features()
        
        # 保存预处理后的数据
        self.preprocessor.save_processed_data()
        
        # 准备训练数据
        data = self.preprocessor.prepare_data_for_training(
            sequence_length=MODEL_CONFIG['sequence_length'],
            train_ratio=MODEL_CONFIG['train_ratio'],
            val_ratio=MODEL_CONFIG['val_ratio']
        )

        # 创建PyTorch数据加载器
        train_loader, val_loader, test_loader = self.preprocessor.create_pytorch_dataloaders(
            data,
            batch_size=MODEL_CONFIG['batch_size']
        )

        # 将数据加载器添加到数据字典中
        data['train_loader'] = train_loader
        data['val_loader'] = val_loader
        data['test_loader'] = test_loader

        print(f"数据准备完成，特征数量: {data['n_features']}")
        print(f"使用的标准化器类型: {type(self.preprocessor.scaler_y).__name__}")
        return data
    
    def train_bigru_model(self, data: dict) -> None:
        """
        训练双向GRU模型
        
        Args:
            data: 训练数据
        """
        print("\n" + "=" * 60)
        print("步骤2: 训练双向GRU模型")
        print("=" * 60)
        
        # 创建双向GRU模型
        self.model = BiGRUModel(
            sequence_length=data['sequence_length'],
            n_features=data['n_features'],
            dropout_rate=MODEL_CONFIG['dropout_rate']
        )

        # 设置标准化器
        self.model.set_scalers(
            self.preprocessor.scaler_X,
            self.preprocessor.scaler_y
        )

        # 训练模型
        self.history = self.model.train_model(
            train_loader=data['train_loader'],
            val_loader=data['val_loader'],
            epochs=MODEL_CONFIG['epochs'],
            patience=MODEL_CONFIG['patience'],
            learning_rate=MODEL_CONFIG['learning_rate']
        )
        
        # 保存模型
        self.model.save_model()
        
        # 评估模型（使用标准化后的指标）
        self.results = self.model.evaluate(
            X_train=data['X_train'],
            y_train=data['y_train'],
            X_val=data['X_val'],
            y_val=data['y_val'],
            X_test=data['X_test'],
            y_test=data['y_test'],
            use_normalized_metrics=True
        )
        
        print("双向GRU模型训练完成！")
    
    def visualize_results(self) -> None:
        """可视化训练结果 - 使用通用可视化系统"""
        print("\n" + "=" * 60)
        print("步骤3: 结果可视化")
        print("=" * 60)

        # 使用新的通用可视化系统
        from src.utils.visualization_manager import visualize_single

        generated_files = visualize_single(
            model_name='BiGRU',
            results=self.results,
            history=self.history,
            experiment_name='BiGRU',
            save_path=get_current_paths()['figures']
        )

        print(f"✅ 通用可视化系统生成了 {len(generated_files)} 个图表文件")
        print("📋 生成的图表:")
        for i, file_path in enumerate(generated_files, 1):
            filename = os.path.basename(file_path)
            print(f"  {i:2d}. {filename}")

        print("\n可视化完成！")
    
    def print_detailed_results(self):
        """打印详细的结果"""
        print("\n" + "=" * 80)
        print("双向GRU模型详细结果 (使用MinMaxScaler标准化目标变量)")
        print("=" * 80)
        
        print(f"\n双向GRU模型结果:")
        print("-" * 40)
        
        for dataset in ['train', 'val', 'test']:
            metrics = self.results[dataset]
            print(f"\n{dataset.capitalize()}集:")
            for metric, value in metrics.items():
                if metric in ['MAPE', 'SMAPE', 'WAPE']:
                    print(f"  {metric}: {value:.4f}%")
                else:
                    print(f"  {metric}: {value:.6f}")
        
        print("\n" + "=" * 80)
        print("双向GRU模型特点:")
        print("- 使用双向GRU层，能够同时利用过去和未来的信息")
        print("- 相比单向GRU，具有更强的序列建模能力")
        print("- 参数量约为单向GRU的两倍，但性能通常更好")
        print("- 使用MinMaxScaler标准化，指标显示为0-1范围内的数值")
        print("=" * 80)
    
    def run_experiment(self) -> None:
        """运行完整的实验流程"""
        print("开始双向GRU风电功率预测模型训练实验")
        print("使用MinMaxScaler对目标变量进行标准化，确保MAE/MSE为归一化结果")
        print("=" * 60)
        
        try:
            # 1. 数据准备
            data = self.load_and_prepare_data()
            
            # 2. 训练双向GRU模型
            self.train_bigru_model(data)
            
            # 3. 可视化结果
            self.visualize_results()
            
            # 4. 打印详细结果
            self.print_detailed_results()
            
            # 找到最佳指标
            test_rmse = self.results['test']['RMSE']
            
            print("\n✅ 双向GRU实验成功完成！")
            print(f"🏆 测试集RMSE: {test_rmse:.6f}")
            print(f"📁 所有结果已保存到: {self.training_paths['results_root']}")
            print(f"🕒 训练会话时间戳: {self.training_paths['timestamp']}")
            
        except Exception as e:
            print(f"\n实验过程中出现错误: {str(e)}")
            import traceback
            traceback.print_exc()

# 主程序
if __name__ == "__main__":
    trainer = BiGRUTrainer()
    trainer.run_experiment()
