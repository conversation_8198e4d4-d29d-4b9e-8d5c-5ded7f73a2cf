"""
ASB-DPTAM优势对比分析实验
对比分析基线BiGRU、DPTAM-BiGRU和ASB-DPTAM-BiGRU模型的性能差异
验证ASB频域增强和DPTAM时序注意力机制的有效性
"""

import os
import sys
import torch
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.data_processing.data_loader import DataLoader
from src.data_processing.preprocessor import DataPreprocessor
from src.models.bigru_model import BiGRUModel
from src.models.dptam_bigru_model import DPTAMBiGRUModel
from src.models.asb_dptam_bigru_model import ASBDPTAMBiGRUModel
from src.utils.visualization_manager import auto_visualize
from src.utils.metrics import ModelEvaluator
from src.utils.config import (
    MODEL_CONFIG, DATASET_CONFIG, BIGRU_CONFIG, DPTAM_BIGRU_CONFIG, ASB_DPTAM_BIGRU_CONFIG,
    setup_matplotlib, setup_training_session, get_current_paths
)


class ASBDPTAMAdvantageComparator:
    """ASB-DPTAM优势对比分析实验类"""
    
    def __init__(self):
        """初始化对比实验"""
        self.models = {}
        self.results = {}
        self.histories = {}
        self.data_loader = None
        self.preprocessor = None
        
        # 设置matplotlib和训练会话
        setup_matplotlib()
        setup_training_session()  # 使用时间戳格式

        print("=" * 80)
        print("ASB-DPTAM优势对比分析实验")
        print("=" * 80)
        print("实验目标:")
        print("• 验证ASB频域增强的有效性")
        print("• 验证DPTAM时序注意力机制的有效性")
        print("• 验证ASB+DPTAM串联架构的额外价值")
        print("• 对比三种模型的性能差异")
        print("=" * 80)

    def load_and_prepare_data(self) -> dict:
        """加载和预处理数据"""
        print("\n步骤1: 数据加载与预处理")
        print("-" * 40)
        
        # 数据加载
        self.data_loader = DataLoader()
        raw_data = self.data_loader.load_data()
        
        print(f"原始数据形状: {raw_data.shape}")

        # 显示原始数据的Power统计信息
        print(f"原始Power数据统计:")
        print(f"  最小值: {raw_data[DATASET_CONFIG['target_column']].min():.4f}")
        print(f"  最大值: {raw_data[DATASET_CONFIG['target_column']].max():.4f}")
        print(f"  平均值: {raw_data[DATASET_CONFIG['target_column']].mean():.4f}")
        print(f"  标准差: {raw_data[DATASET_CONFIG['target_column']].std():.4f}")

        # 数据分析（跳过可视化以避免阻塞）
        self.data_loader.analyze_data()

        # 数据预处理
        self.preprocessor = DataPreprocessor()
        self.preprocessor.set_data(raw_data)
        df_processed = self.preprocessor.preprocess()
        feature_columns = self.preprocessor.select_features()

        # 保存预处理后的数据
        self.preprocessor.save_processed_data()

        # 准备训练数据
        processed_data = self.preprocessor.prepare_data_for_training(
            sequence_length=MODEL_CONFIG['sequence_length'],
            train_ratio=MODEL_CONFIG['train_ratio'],
            val_ratio=MODEL_CONFIG['val_ratio']
        )

        # 创建PyTorch数据加载器
        train_loader, val_loader, test_loader = self.preprocessor.create_pytorch_dataloaders(
            processed_data,
            batch_size=MODEL_CONFIG['batch_size']
        )

        # 将数据加载器添加到数据字典中
        processed_data['train_loader'] = train_loader
        processed_data['val_loader'] = val_loader
        processed_data['test_loader'] = test_loader

        # 保存测试数据加载器为类属性，用于后续可视化
        self.test_loader = test_loader
        
        print(f"数据准备完成，特征数量: {processed_data['n_features']}")
        print(f"使用的标准化器类型: {type(self.preprocessor.scaler_y).__name__}")
        print(f"序列长度: {processed_data['sequence_length']}")
        print(f"训练集大小: {len(processed_data['train_loader'].dataset)}")
        print(f"验证集大小: {len(processed_data['val_loader'].dataset)}")
        print(f"测试集大小: {len(processed_data['test_loader'].dataset)}")

        return processed_data

    def train_baseline_bigru(self, data: dict) -> None:
        """训练基线BiGRU模型"""
        print("\n步骤2: 训练基线BiGRU模型")
        print("-" * 40)
        
        # 创建基线BiGRU模型
        self.models['Baseline-BiGRU'] = BiGRUModel(
            sequence_length=data['sequence_length'],
            n_features=data['n_features'],
            bigru_units=BIGRU_CONFIG['bigru_units'],
            dense_units=BIGRU_CONFIG['dense_units'],
            dropout_rate=BIGRU_CONFIG['dropout_rate']
        )
        
        # 设置标准化器
        self.models['Baseline-BiGRU'].set_scalers(
            self.preprocessor.scaler_X,
            self.preprocessor.scaler_y
        )
        
        # 训练模型
        self.histories['Baseline-BiGRU'] = self.models['Baseline-BiGRU'].train_model(
            train_loader=data['train_loader'],
            val_loader=data['val_loader'],
            epochs=MODEL_CONFIG['epochs'],
            patience=MODEL_CONFIG['patience'],
            learning_rate=MODEL_CONFIG['learning_rate']
        )
        
        # 评估模型
        self.results['Baseline-BiGRU'] = self.models['Baseline-BiGRU'].evaluate(
            X_train=data['X_train'],
            y_train=data['y_train'],
            X_val=data['X_val'],
            y_val=data['y_val'],
            X_test=data['X_test'],
            y_test=data['y_test'],
            use_normalized_metrics=True
        )
        
        # 保存基线BiGRU模型
        baseline_model_path = os.path.join(get_current_paths()['models'], 'baseline_bigru_model.pth')
        self.models['Baseline-BiGRU'].save_model(baseline_model_path)

        baseline_info = self.models['Baseline-BiGRU'].get_model_info()
        print(f"✅ 基线BiGRU训练完成")
        print(f"   参数量: {baseline_info['total_params']:,}")
        print(f"   测试RMSE: {self.results['Baseline-BiGRU']['test']['RMSE']:.6f}")
        print(f"   模型已保存: {baseline_model_path}")

    def train_dptam_bigru(self, data: dict) -> None:
        """训练DPTAM-BiGRU模型"""
        print("\n步骤3: 训练DPTAM-BiGRU模型")
        print("-" * 40)
        
        # 创建DPTAM-BiGRU模型
        self.models['DPTAM-BiGRU'] = DPTAMBiGRUModel(
            sequence_length=data['sequence_length'],
            n_features=data['n_features'],
            n_segment=DPTAM_BIGRU_CONFIG['n_segment'],
            dptam_kernel_size=DPTAM_BIGRU_CONFIG['dptam_kernel_size'],
            bigru_units=DPTAM_BIGRU_CONFIG['bigru_units'],
            dense_units=DPTAM_BIGRU_CONFIG['dense_units'],
            dropout_rate=DPTAM_BIGRU_CONFIG['dropout_rate']
        )
        
        # 设置标准化器
        self.models['DPTAM-BiGRU'].set_scalers(
            self.preprocessor.scaler_X,
            self.preprocessor.scaler_y
        )
        
        # 训练模型
        self.histories['DPTAM-BiGRU'] = self.models['DPTAM-BiGRU'].train_model(
            train_loader=data['train_loader'],
            val_loader=data['val_loader'],
            epochs=MODEL_CONFIG['epochs'],
            patience=MODEL_CONFIG['patience'],
            learning_rate=MODEL_CONFIG['learning_rate']
        )
        
        # 评估模型
        self.results['DPTAM-BiGRU'] = self.models['DPTAM-BiGRU'].evaluate(
            X_train=data['X_train'],
            y_train=data['y_train'],
            X_val=data['X_val'],
            y_val=data['y_val'],
            X_test=data['X_test'],
            y_test=data['y_test'],
            use_normalized_metrics=True
        )
        
        # 保存DPTAM-BiGRU模型
        dptam_model_path = os.path.join(get_current_paths()['models'], 'dptam_bigru_model.pth')
        self.models['DPTAM-BiGRU'].save_model(dptam_model_path)

        dptam_info = self.models['DPTAM-BiGRU'].get_model_info()
        print(f"✅ DPTAM-BiGRU训练完成")
        print(f"   参数量: {dptam_info['total_params']:,}")
        print(f"   测试RMSE: {self.results['DPTAM-BiGRU']['test']['RMSE']:.6f}")
        print(f"   模型已保存: {dptam_model_path}")

    def train_asb_dptam_bigru(self, data: dict) -> None:
        """训练ASB-DPTAM-BiGRU模型"""
        print("\n步骤4: 训练ASB-DPTAM-BiGRU模型")
        print("-" * 40)
        
        # 创建ASB-DPTAM-BiGRU模型
        self.models['ASB-DPTAM-BiGRU'] = ASBDPTAMBiGRUModel(
            sequence_length=data['sequence_length'],
            n_features=data['n_features'],
            n_segment=ASB_DPTAM_BIGRU_CONFIG['n_segment'],
            dptam_kernel_size=ASB_DPTAM_BIGRU_CONFIG['dptam_kernel_size'],
            bigru_units=ASB_DPTAM_BIGRU_CONFIG['bigru_units'],
            dense_units=ASB_DPTAM_BIGRU_CONFIG['dense_units'],
            dropout_rate=ASB_DPTAM_BIGRU_CONFIG['dropout_rate'],
            asb_adaptive_filter=ASB_DPTAM_BIGRU_CONFIG['asb_adaptive_filter']
        )
        
        # 设置标准化器
        self.models['ASB-DPTAM-BiGRU'].set_scalers(
            self.preprocessor.scaler_X,
            self.preprocessor.scaler_y
        )
        
        # 训练模型
        self.histories['ASB-DPTAM-BiGRU'] = self.models['ASB-DPTAM-BiGRU'].train_model(
            train_loader=data['train_loader'],
            val_loader=data['val_loader'],
            epochs=MODEL_CONFIG['epochs'],
            patience=MODEL_CONFIG['patience'],
            learning_rate=MODEL_CONFIG['learning_rate']
        )
        
        # 评估模型
        self.results['ASB-DPTAM-BiGRU'] = self.models['ASB-DPTAM-BiGRU'].evaluate(
            X_train=data['X_train'],
            y_train=data['y_train'],
            X_val=data['X_val'],
            y_val=data['y_val'],
            X_test=data['X_test'],
            y_test=data['y_test'],
            use_normalized_metrics=True
        )
        
        # 保存ASB-DPTAM-BiGRU模型
        asb_model_path = os.path.join(get_current_paths()['models'], 'asb_dptam_bigru_model.pth')
        self.models['ASB-DPTAM-BiGRU'].save_model(asb_model_path)

        asb_info = self.models['ASB-DPTAM-BiGRU'].get_model_info()
        print(f"✅ ASB-DPTAM-BiGRU训练完成")
        print(f"   参数量: {asb_info['total_params']:,}")
        print(f"   测试RMSE: {self.results['ASB-DPTAM-BiGRU']['test']['RMSE']:.6f}")
        print(f"   模型已保存: {asb_model_path}")

    def analyze_advantages(self) -> None:
        """分析各模型的优势"""
        print("\n步骤5: ASB-DPTAM优势对比分析")
        print("-" * 40)

        # 获取基线性能
        baseline_rmse = self.results['Baseline-BiGRU']['test']['RMSE']
        baseline_r2 = self.results['Baseline-BiGRU']['test']['R2']
        baseline_mae = self.results['Baseline-BiGRU']['test']['MAE']

        print(f"📊 性能对比分析:")
        print(f"基线BiGRU (无增强):")
        print(f"  RMSE: {baseline_rmse:.6f}")
        print(f"  R²:   {baseline_r2:.6f}")
        print(f"  MAE:  {baseline_mae:.6f}")

        # DPTAM vs Baseline (验证时序注意力机制的效果)
        if 'DPTAM-BiGRU' in self.results:
            dptam_rmse = self.results['DPTAM-BiGRU']['test']['RMSE']
            dptam_r2 = self.results['DPTAM-BiGRU']['test']['R2']
            dptam_mae = self.results['DPTAM-BiGRU']['test']['MAE']

            dptam_rmse_improvement = ((baseline_rmse - dptam_rmse) / baseline_rmse) * 100
            dptam_r2_improvement = ((dptam_r2 - baseline_r2) / baseline_r2) * 100
            dptam_mae_improvement = ((baseline_mae - dptam_mae) / baseline_mae) * 100

            print(f"\nDPTAM-BiGRU (时序注意力增强):")
            print(f"  RMSE: {dptam_rmse:.6f} (改进: {dptam_rmse_improvement:+.2f}%)")
            print(f"  R²:   {dptam_r2:.6f} (改进: {dptam_r2_improvement:+.2f}%)")
            print(f"  MAE:  {dptam_mae:.6f} (改进: {dptam_mae_improvement:+.2f}%)")

            if dptam_rmse_improvement > 0:
                print(f"  ✅ DPTAM时序注意力机制有效！")
            else:
                print(f"  ⚠️ DPTAM时序注意力机制效果有限")
        
        # ASB-DPTAM vs Baseline and DPTAM (验证频域增强+时序注意力的综合效果)
        if 'ASB-DPTAM-BiGRU' in self.results:
            asb_rmse = self.results['ASB-DPTAM-BiGRU']['test']['RMSE']
            asb_r2 = self.results['ASB-DPTAM-BiGRU']['test']['R2']
            asb_mae = self.results['ASB-DPTAM-BiGRU']['test']['MAE']

            asb_rmse_improvement = ((baseline_rmse - asb_rmse) / baseline_rmse) * 100
            asb_r2_improvement = ((asb_r2 - baseline_r2) / baseline_r2) * 100
            asb_mae_improvement = ((baseline_mae - asb_mae) / baseline_mae) * 100

            print(f"\nASB-DPTAM-BiGRU (频域+时序双重增强):")
            print(f"  RMSE: {asb_rmse:.6f} (改进: {asb_rmse_improvement:+.2f}%)")
            print(f"  R²:   {asb_r2:.6f} (改进: {asb_r2_improvement:+.2f}%)")
            print(f"  MAE:  {asb_mae:.6f} (改进: {asb_mae_improvement:+.2f}%)")

            if asb_rmse_improvement > 0:
                print(f"  ✅ ASB+DPTAM串联架构有效！")
            else:
                print(f"  ⚠️ ASB+DPTAM串联架构效果有限")

            # ASB相对于DPTAM的额外改进 (验证ASB频域增强的独立贡献)
            if 'DPTAM-BiGRU' in self.results:
                asb_vs_dptam_rmse = ((dptam_rmse - asb_rmse) / dptam_rmse) * 100
                asb_vs_dptam_r2 = ((asb_r2 - dptam_r2) / dptam_r2) * 100
                asb_vs_dptam_mae = ((dptam_mae - asb_mae) / dptam_mae) * 100

                print(f"\n🔥 ASB频域增强的独立贡献 (相对DPTAM的额外改进):")
                print(f"  RMSE: {asb_vs_dptam_rmse:+.2f}%")
                print(f"  R²:   {asb_vs_dptam_r2:+.2f}%")
                print(f"  MAE:  {asb_vs_dptam_mae:+.2f}%")

                if asb_vs_dptam_rmse > 0:
                    print(f"  ✅ ASB频域增强成功进一步提升了DPTAM性能！")
                    print(f"  💡 频域降噪+时序注意力的串联架构确实有效！")
                else:
                    print(f"  ⚠️ ASB频域增强未能进一步提升DPTAM性能")
                    print(f"  💭 可能需要调优ASB参数或数据特性不适合频域处理")

    def generate_comparison_report(self) -> None:
        """生成对比报告"""
        print("\n步骤6: 生成对比报告")
        print("-" * 40)
        
        # 计算改进指标
        baseline_rmse = self.results['Baseline-BiGRU']['test']['RMSE']
        
        improvements = {}
        for model_name in ['DPTAM-BiGRU', 'ASB-DPTAM-BiGRU']:
            if model_name in self.results:
                model_rmse = self.results[model_name]['test']['RMSE']
                improvement = ((baseline_rmse - model_rmse) / baseline_rmse) * 100
                improvements[model_name] = improvement
        
        # 生成报告内容
        report_content = f"""# ASB-DPTAM优势对比分析报告

## 实验概述
本实验对比了三种风电功率预测模型的性能：
1. **Baseline-BiGRU**: 基线双向GRU模型 (无增强)
2. **DPTAM-BiGRU**: 加入DPTAM时序注意力机制 (时序增强)
3. **ASB-DPTAM-BiGRU**: ASB频域增强+DPTAM时序注意力的串联架构 (双重增强)

## 模型配置
- **序列长度**: {MODEL_CONFIG['sequence_length']}
- **训练轮数**: {MODEL_CONFIG['epochs']}
- **学习率**: {MODEL_CONFIG['learning_rate']}
- **批次大小**: {MODEL_CONFIG['batch_size']}

## 性能对比结果

### 测试集性能指标
| 模型 | RMSE | R² | MAE | 参数量 |
|------|------|----|----|--------|
| Baseline-BiGRU | {self.results['Baseline-BiGRU']['test']['RMSE']:.6f} | {self.results['Baseline-BiGRU']['test']['R2']:.6f} | {self.results['Baseline-BiGRU']['test']['MAE']:.6f} | {self.models['Baseline-BiGRU'].get_model_info()['total_params']:,} |
"""
        
        if 'DPTAM-BiGRU' in self.results:
            report_content += f"| DPTAM-BiGRU | {self.results['DPTAM-BiGRU']['test']['RMSE']:.6f} | {self.results['DPTAM-BiGRU']['test']['R2']:.6f} | {self.results['DPTAM-BiGRU']['test']['MAE']:.6f} | {self.models['DPTAM-BiGRU'].get_model_info()['total_params']:,} |\n"
        
        if 'ASB-DPTAM-BiGRU' in self.results:
            report_content += f"| ASB-DPTAM-BiGRU | {self.results['ASB-DPTAM-BiGRU']['test']['RMSE']:.6f} | {self.results['ASB-DPTAM-BiGRU']['test']['R2']:.6f} | {self.results['ASB-DPTAM-BiGRU']['test']['MAE']:.6f} | {self.models['ASB-DPTAM-BiGRU'].get_model_info()['total_params']:,} |\n"
        
        report_content += f"""
### 性能改进分析
"""
        
        for model_name, improvement in improvements.items():
            status = "✅ 有效提升" if improvement > 0 else "⚠️ 未能提升"
            report_content += f"- **{model_name}**: {improvement:+.2f}% {status}\n"
        
        report_content += f"""
## 结论

### 1. DPTAM时序注意力机制验证
{'✅ **验证成功**: DPTAM时序注意力机制有效提升了预测性能' if improvements.get('DPTAM-BiGRU', 0) > 0 else '❌ **验证失败**: DPTAM在当前配置下未能提升性能'}
- **机制**: 分段时序注意力，突出重要时间段
- **效果**: {'有效识别时序模式' if improvements.get('DPTAM-BiGRU', 0) > 0 else '可能需要调整分段策略或参数'}

### 2. ASB频域增强验证
{'✅ **验证成功**: ASB频域增强进一步提升了模型性能' if improvements.get('ASB-DPTAM-BiGRU', 0) > improvements.get('DPTAM-BiGRU', 0) else '⚠️ **需要优化**: ASB频域增强效果有限，可能需要调优'}
- **机制**: 自适应频谱滤波，降噪增强
- **效果**: {'有效减少噪声干扰，提升信号质量' if improvements.get('ASB-DPTAM-BiGRU', 0) > improvements.get('DPTAM-BiGRU', 0) else '可能数据特性不适合频域处理或需要参数调优'}

### 3. ASB+DPTAM串联架构验证
{'✅ **验证成功**: 串联架构实现了频域+时序双重增强' if improvements.get('ASB-DPTAM-BiGRU', 0) > 0 else '⚠️ **需要优化**: 串联架构效果有限'}
- **架构**: ASB频域降噪 → DPTAM时序注意力 → BiGRU双向建模
- **优势**: {'渐进式处理策略有效，先降噪再注意力' if improvements.get('ASB-DPTAM-BiGRU', 0) > 0 else '可能需要优化处理顺序或组件参数'}

### 4. 技术洞察
- **时序注意力**: {'有效识别重要时间段，提升时序建模能力' if improvements.get('DPTAM-BiGRU', 0) > 0 else '可能需要调整分段策略或注意力机制参数'}
- **频域处理**: {'有效减少噪声干扰，为后续处理提供清洁信号' if improvements.get('ASB-DPTAM-BiGRU', 0) > improvements.get('DPTAM-BiGRU', 0) else '可能数据特性不适合频域处理或自适应阈值需要调优'}
- **串联架构**: {'渐进式处理策略优于并联，信息流清晰' if improvements.get('ASB-DPTAM-BiGRU', 0) > 0 else '可能需要优化处理顺序或考虑并联架构'}
- **参数效率**: {'串联架构参数更少，计算更高效' if improvements.get('ASB-DPTAM-BiGRU', 0) > 0 else '需要在性能和效率间找到平衡'}

实验时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        # 保存报告
        report_path = os.path.join(get_current_paths()['reports'], 'asb_dptam_advantage_comparison_report.md')
        os.makedirs(os.path.dirname(report_path), exist_ok=True)

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)

        print(f"✅ ASB-DPTAM优势对比报告已保存至: {report_path}")

    def generate_visualizations(self) -> None:
        """生成可视化结果图表"""
        print("\n步骤6: 生成可视化结果")
        print("-" * 40)

        try:
            # 准备models_data格式的数据
            models_data = {}
            for model_name, model_wrapper in self.models.items():
                if model_name in self.results and hasattr(model_wrapper, 'history'):
                    models_data[model_name] = (self.results[model_name], model_wrapper.history)
                    print(f"📊 准备 {model_name} 可视化数据...")

            if models_data:
                # 使用auto_visualize生成标准可视化图表
                print("🎨 生成完整可视化套件...")
                generated_files = auto_visualize(
                    models_data=models_data,
                    experiment_name="ASB_DPTAM_Advantage_Comparison",
                    save_path=get_current_paths()['figures']
                )

                # 生成性能对比图表
                self.create_performance_comparison_plots()

                print("✅ 所有可视化结果已生成")
                print(f"📊 生成的图表文件数量: {len(generated_files.get('universal', [])) + len(generated_files.get('legacy', []))}")
            else:
                print("⚠️ 没有可用的模型数据，跳过可视化")

        except Exception as e:
            print(f"❌ 生成可视化时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()

    def create_performance_comparison_plots(self) -> None:
        """创建性能对比图表"""
        try:
            import matplotlib.pyplot as plt
            import numpy as np

            # 提取性能指标
            models = list(self.results.keys())
            rmse_values = [self.results[model]['test']['RMSE'] for model in models]
            r2_values = [self.results[model]['test']['R²'] for model in models]
            mae_values = [self.results[model]['test']['MAE'] for model in models]

            # 创建性能对比柱状图
            fig, axes = plt.subplots(1, 3, figsize=(15, 5))

            # RMSE对比
            axes[0].bar(models, rmse_values, color=['#1f77b4', '#ff7f0e', '#2ca02c'])
            axes[0].set_title('RMSE 对比', fontsize=14, fontweight='bold')
            axes[0].set_ylabel('RMSE')
            axes[0].tick_params(axis='x', rotation=45)

            # R²对比
            axes[1].bar(models, r2_values, color=['#1f77b4', '#ff7f0e', '#2ca02c'])
            axes[1].set_title('R² 对比', fontsize=14, fontweight='bold')
            axes[1].set_ylabel('R²')
            axes[1].tick_params(axis='x', rotation=45)

            # MAE对比
            axes[2].bar(models, mae_values, color=['#1f77b4', '#ff7f0e', '#2ca02c'])
            axes[2].set_title('MAE 对比', fontsize=14, fontweight='bold')
            axes[2].set_ylabel('MAE')
            axes[2].tick_params(axis='x', rotation=45)

            plt.tight_layout()

            # 保存图表
            comparison_path = os.path.join(get_current_paths()['figures'], 'performance_comparison.png')
            plt.savefig(comparison_path, dpi=300, bbox_inches='tight')
            plt.close()

            print(f"📊 性能对比图表已保存: {comparison_path}")

        except Exception as e:
            print(f"❌ 创建性能对比图表时发生错误: {str(e)}")

    def run_comparison_experiment(self) -> None:
        """运行完整的对比实验"""
        try:
            # 1. 数据准备
            data = self.load_and_prepare_data()
            
            # 2. 训练基线模型
            self.train_baseline_bigru(data)
            
            # 3. 训练DPTAM模型
            self.train_dptam_bigru(data)
            
            # 4. 训练ASB-DPTAM模型
            self.train_asb_dptam_bigru(data)
            
            # 5. 分析优势
            self.analyze_advantages()
            
            # 6. 生成可视化结果
            self.generate_visualizations()

            # 7. 生成报告
            self.generate_comparison_report()

            print(f"\n🎉 ASB-DPTAM优势对比实验完成！")
            print(f"📁 所有结果已保存到: {get_current_paths()['results_root']}")

            # 实验总结
            print(f"\n📋 实验总结:")
            print(f"✅ 基线BiGRU: 无增强的基础模型")
            print(f"✅ DPTAM-BiGRU: 验证时序注意力机制的效果")
            print(f"✅ ASB-DPTAM-BiGRU: 验证频域+时序双重增强的效果")
            print(f"💡 通过对比分析，可以清楚看到每个组件的独立贡献")

        except Exception as e:
            print(f"\n❌ 实验过程中发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
            raise


def main():
    """主函数"""
    comparator = ASBDPTAMAdvantageComparator()
    comparator.run_comparison_experiment()


if __name__ == "__main__":
    main()
