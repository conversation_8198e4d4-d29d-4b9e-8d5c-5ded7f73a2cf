"""
DPTAM-BiGRU与原始BiGRU模型对比实验
评估DPTAM时序注意力机制对BiGRU模型性能的提升效果
"""

import os
import sys
import numpy as np
import pandas as pd
import torch
import warnings
import matplotlib.pyplot as plt
warnings.filterwarnings('ignore')

# 设置随机种子
torch.manual_seed(42)
np.random.seed(42)

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.data_processing.data_loader import DataLoader
from src.data_processing.preprocessor import DataPreprocessor
from src.models.bigru_model import BiGRUModel
from src.models.dptam_bigru_model import DPTAMBiGRUModel
from src.utils.visualization import AcademicVisualizer
from src.utils.metrics import ModelEvaluator
from src.utils.config import (
    MODEL_CONFIG, DATASET_CONFIG, BIGRU_CONFIG, DPTAM_BIGRU_CONFIG, setup_matplotlib,
    setup_training_session, get_current_paths
)

class ModelComparator:
    """模型对比实验类"""
    
    def __init__(self):
        """初始化对比器"""
        # 设置时间戳训练会话
        self.training_paths = setup_training_session()
        print(f"🕒 对比实验时间戳: {self.training_paths['timestamp']}")
        print(f"📁 结果保存路径: {self.training_paths['results_root']}")

        self.data_loader = DataLoader()
        self.preprocessor = DataPreprocessor()
        self.visualizer = AcademicVisualizer()
        self.evaluator = ModelEvaluator()
        self.models = {}
        self.histories = {}
        self.results = {}

        # 设置matplotlib
        setup_matplotlib()

        print("✅ DPTAM-BiGRU vs BiGRU 模型对比器初始化完成")
    
    def load_and_prepare_data(self) -> dict:
        """
        加载和准备数据
        
        Returns:
            准备好的训练数据
        """
        print("\n" + "=" * 60)
        print("步骤1: 数据加载和预处理")
        print("=" * 60)
        
        # 加载数据
        df = self.data_loader.load_data()
        
        # 显示原始数据的Power统计信息
        print(f"原始Power数据统计:")
        print(f"  最小值: {df['Power'].min():.4f}")
        print(f"  最大值: {df['Power'].max():.4f}")
        print(f"  平均值: {df['Power'].mean():.4f}")
        print(f"  标准差: {df['Power'].std():.4f}")
        
        # 数据预处理
        self.preprocessor.set_data(df)
        df_processed = self.preprocessor.preprocess()
        feature_columns = self.preprocessor.select_features()
        
        # 保存预处理后的数据
        self.preprocessor.save_processed_data()
        
        # 准备训练数据
        data = self.preprocessor.prepare_data_for_training(
            sequence_length=MODEL_CONFIG['sequence_length'],
            train_ratio=MODEL_CONFIG['train_ratio'],
            val_ratio=MODEL_CONFIG['val_ratio']
        )

        # 创建PyTorch数据加载器
        train_loader, val_loader, test_loader = self.preprocessor.create_pytorch_dataloaders(
            data,
            batch_size=MODEL_CONFIG['batch_size']
        )

        # 将数据加载器添加到数据字典中
        data['train_loader'] = train_loader
        data['val_loader'] = val_loader
        data['test_loader'] = test_loader

        print(f"数据准备完成，特征数量: {data['n_features']}")
        return data
    
    def train_bigru_model(self, data: dict) -> None:
        """
        训练原始BiGRU模型
        
        Args:
            data: 训练数据
        """
        print("\n" + "=" * 60)
        print("步骤2: 训练原始BiGRU模型")
        print("=" * 60)
        
        # 创建BiGRU模型（使用config.py中的配置）
        self.models['BiGRU'] = BiGRUModel(
            sequence_length=data['sequence_length'],
            n_features=data['n_features'],
            bigru_units=BIGRU_CONFIG['bigru_units'],
            dense_units=BIGRU_CONFIG['dense_units'],
            dropout_rate=BIGRU_CONFIG['dropout_rate']
        )

        # 设置标准化器
        self.models['BiGRU'].set_scalers(
            self.preprocessor.scaler_X,
            self.preprocessor.scaler_y
        )

        # 训练模型
        self.histories['BiGRU'] = self.models['BiGRU'].train_model(
            train_loader=data['train_loader'],
            val_loader=data['val_loader'],
            epochs=MODEL_CONFIG['epochs'],
            patience=MODEL_CONFIG['patience'],
            learning_rate=MODEL_CONFIG['learning_rate']
        )
        
        # 评估模型
        self.results['BiGRU'] = self.models['BiGRU'].evaluate(
            X_train=data['X_train'],
            y_train=data['y_train'],
            X_val=data['X_val'],
            y_val=data['y_val'],
            X_test=data['X_test'],
            y_test=data['y_test'],
            use_normalized_metrics=True
        )
        
        print("BiGRU模型训练完成！")
    
    def train_dptam_bigru_model(self, data: dict) -> None:
        """
        训练DPTAM-BiGRU融合模型
        
        Args:
            data: 训练数据
        """
        print("\n" + "=" * 60)
        print("步骤3: 训练DPTAM-BiGRU融合模型")
        print("=" * 60)
        
        # 创建DPTAM-BiGRU融合模型（使用config.py中的配置）
        self.models['DPTAM-BiGRU'] = DPTAMBiGRUModel(
            sequence_length=data['sequence_length'],
            n_features=data['n_features'],
            n_segment=DPTAM_BIGRU_CONFIG['n_segment'],
            dptam_kernel_size=DPTAM_BIGRU_CONFIG['dptam_kernel_size'],
            bigru_units=DPTAM_BIGRU_CONFIG['bigru_units'],
            dense_units=DPTAM_BIGRU_CONFIG['dense_units'],
            dropout_rate=DPTAM_BIGRU_CONFIG['dropout_rate']
        )

        # 设置标准化器
        self.models['DPTAM-BiGRU'].set_scalers(
            self.preprocessor.scaler_X,
            self.preprocessor.scaler_y
        )

        # 训练模型
        self.histories['DPTAM-BiGRU'] = self.models['DPTAM-BiGRU'].train_model(
            train_loader=data['train_loader'],
            val_loader=data['val_loader'],
            epochs=MODEL_CONFIG['epochs'],
            patience=MODEL_CONFIG['patience'],
            learning_rate=MODEL_CONFIG['learning_rate']
        )
        
        # 评估模型
        self.results['DPTAM-BiGRU'] = self.models['DPTAM-BiGRU'].evaluate(
            X_train=data['X_train'],
            y_train=data['y_train'],
            X_val=data['X_val'],
            y_val=data['y_val'],
            X_test=data['X_test'],
            y_test=data['y_test'],
            use_normalized_metrics=True
        )
        
        print("DPTAM-BiGRU融合模型训练完成！")
    
    def compare_models(self) -> pd.DataFrame:
        """
        对比两个模型的性能
        
        Returns:
            对比结果DataFrame
        """
        print("\n" + "=" * 60)
        print("步骤4: 模型性能对比")
        print("=" * 60)
        
        # 创建对比表格
        comparison_data = []
        
        for model_name in ['BiGRU', 'DPTAM-BiGRU']:
            if model_name in self.results:
                row = {'Model': model_name}
                
                # 添加测试集指标
                for metric in ['MAE', 'MSE', 'RMSE', 'R2']:
                    if metric in self.results[model_name]['test']:
                        row[f'Test_{metric}'] = self.results[model_name]['test'][metric]
                
                # 添加模型信息
                model_info = self.models[model_name].get_model_info()
                row['Total_Params'] = model_info['total_params']
                row['Training_Epochs'] = len(self.histories[model_name]['train_loss'])
                row['Best_Val_Loss'] = min(self.histories[model_name]['val_loss'])
                
                comparison_data.append(row)
        
        comparison_df = pd.DataFrame(comparison_data)
        
        # 计算改进百分比
        if len(comparison_df) == 2:
            bigru_rmse = comparison_df[comparison_df['Model'] == 'BiGRU']['Test_RMSE'].iloc[0]
            dptam_rmse = comparison_df[comparison_df['Model'] == 'DPTAM-BiGRU']['Test_RMSE'].iloc[0]
            improvement = ((bigru_rmse - dptam_rmse) / bigru_rmse) * 100
            
            print(f"性能对比结果:")
            print(f"  BiGRU RMSE: {bigru_rmse:.6f}")
            print(f"  DPTAM-BiGRU RMSE: {dptam_rmse:.6f}")
            print(f"  RMSE改进: {improvement:.2f}%")
        
        return comparison_df
    
    def visualize_comparison(self, comparison_df: pd.DataFrame) -> None:
        """
        可视化模型对比结果 - 使用通用可视化系统

        Args:
            comparison_df: 对比结果DataFrame
        """
        print("\n" + "=" * 60)
        print("步骤5: 对比结果可视化")
        print("=" * 60)

        # 使用新的通用可视化系统
        from src.utils.visualization_manager import auto_visualize

        # 准备模型数据
        models_data = {}
        for model_name in ['BiGRU', 'DPTAM-BiGRU']:
            if model_name in self.results and model_name in self.histories:
                models_data[model_name] = (self.results[model_name], self.histories[model_name])

        if models_data:
            generated_files = auto_visualize(
                models_data=models_data,
                experiment_name='BiGRU_vs_DPTAM',
                save_path=get_current_paths()['figures']
            )

            total_files = len(generated_files['universal']) + len(generated_files['legacy'])
            print(f"✅ 通用可视化系统生成了 {total_files} 个图表文件")

            if generated_files['universal']:
                print("📋 通用可视化图表:")
                for i, file_path in enumerate(generated_files['universal'], 1):
                    filename = os.path.basename(file_path)
                    print(f"  {i:2d}. {filename}")

            if generated_files['legacy']:
                print("📋 传统可视化图表:")
                for i, file_path in enumerate(generated_files['legacy'], 1):
                    filename = os.path.basename(file_path)
                    print(f"  {i:2d}. {filename}")
        else:
            print("⚠️ 没有找到可用的模型数据，跳过可视化")

        print("\n对比结果可视化完成！")
    
    def print_detailed_comparison(self, comparison_df: pd.DataFrame) -> None:
        """
        打印详细的对比结果
        
        Args:
            comparison_df: 对比结果DataFrame
        """
        print("\n" + "=" * 60)
        print("步骤6: 详细对比结果")
        print("=" * 60)
        
        print("模型对比表:")
        print(comparison_df.to_string(index=False, float_format='%.6f'))
        
        # 计算各项指标的改进
        if len(comparison_df) == 2:
            bigru_row = comparison_df[comparison_df['Model'] == 'BiGRU'].iloc[0]
            dptam_row = comparison_df[comparison_df['Model'] == 'DPTAM-BiGRU'].iloc[0]
            
            print(f"\n性能改进分析:")
            
            metrics = ['Test_MAE', 'Test_RMSE', 'Test_MSE']
            for metric in metrics:
                if metric in bigru_row and metric in dptam_row:
                    bigru_val = bigru_row[metric]
                    dptam_val = dptam_row[metric]
                    improvement = ((bigru_val - dptam_val) / bigru_val) * 100
                    print(f"  {metric}: {improvement:+.2f}% ({'改进' if improvement > 0 else '下降'})")
            
            # R²改进
            r2_improvement = ((dptam_row['Test_R2'] - bigru_row['Test_R2']) / bigru_row['Test_R2']) * 100
            print(f"  Test_R2: {r2_improvement:+.2f}% ({'改进' if r2_improvement > 0 else '下降'})")
            
            # 参数量对比
            param_increase = ((dptam_row['Total_Params'] - bigru_row['Total_Params']) / bigru_row['Total_Params']) * 100
            print(f"  参数量增加: {param_increase:+.2f}%")
            
            print(f"\n关键发现:")
            if improvement > 0:
                print(f"  ✅ DPTAM-BiGRU在RMSE上相比BiGRU改进了{improvement:.2f}%")
            else:
                print(f"  ❌ DPTAM-BiGRU在RMSE上相比BiGRU下降了{abs(improvement):.2f}%")
            
            print(f"  📊 参数量增加了{param_increase:.2f}%，增加了{dptam_row['Total_Params'] - bigru_row['Total_Params']:,}个参数")
            
            if r2_improvement > 0:
                print(f"  📈 R²决定系数提升了{r2_improvement:.2f}%")
            else:
                print(f"  📉 R²决定系数下降了{abs(r2_improvement):.2f}%")

    def generate_comparison_report(self, comparison_df: pd.DataFrame) -> None:
        """
        生成对比实验报告

        Args:
            comparison_df: 对比结果DataFrame
        """
        print("\n" + "=" * 60)
        print("步骤7: 生成对比实验报告")
        print("=" * 60)

        # 计算改进指标
        if len(comparison_df) == 2:
            bigru_row = comparison_df[comparison_df['Model'] == 'BiGRU'].iloc[0]
            dptam_row = comparison_df[comparison_df['Model'] == 'DPTAM-BiGRU'].iloc[0]

            rmse_improvement = ((bigru_row['Test_RMSE'] - dptam_row['Test_RMSE']) / bigru_row['Test_RMSE']) * 100
            mae_improvement = ((bigru_row['Test_MAE'] - dptam_row['Test_MAE']) / bigru_row['Test_MAE']) * 100
            r2_improvement = ((dptam_row['Test_R2'] - bigru_row['Test_R2']) / bigru_row['Test_R2']) * 100
            param_increase = ((dptam_row['Total_Params'] - bigru_row['Total_Params']) / bigru_row['Total_Params']) * 100

        # 生成报告内容
        report_content = f"""
# DPTAM-BiGRU vs BiGRU 模型对比实验报告

## 实验概述
本实验对比了DPTAM-BiGRU融合模型与原始BiGRU模型在风电功率预测任务上的性能表现，评估DPTAM时序注意力机制对模型性能的提升效果。

## 实验设置
- **数据集**: Site_1_standardized.csv
- **序列长度**: {MODEL_CONFIG['sequence_length']}
- **训练配置**: 批次大小{MODEL_CONFIG['batch_size']}, 学习率{MODEL_CONFIG['learning_rate']}, 最大轮数{MODEL_CONFIG['epochs']}
- **数据划分**: 训练{MODEL_CONFIG['train_ratio']:.1f} / 验证{MODEL_CONFIG['val_ratio']:.1f} / 测试{MODEL_CONFIG['test_ratio']:.1f}

## 模型配置对比

### BiGRU基线模型
- **架构**: 双向GRU + 全连接层
- **GRU层**: [128, 64]
- **全连接层**: [64, 32]
- **参数量**: {bigru_row['Total_Params']:,}

### DPTAM-BiGRU融合模型
- **架构**: DPTAM时序注意力 + 双向GRU + 全连接层
- **DPTAM分段**: 4段 (每段6个时间步)
- **GRU层**: [128, 64]
- **全连接层**: [64, 32]
- **参数量**: {dptam_row['Total_Params']:,}
- **参数增加**: {param_increase:+.2f}%

## 实验结果

### 测试集性能对比

| 模型 | MAE | RMSE | R² | 参数量 |
|------|-----|------|----|----|
| BiGRU | {bigru_row['Test_MAE']:.6f} | {bigru_row['Test_RMSE']:.6f} | {bigru_row['Test_R2']:.6f} | {bigru_row['Total_Params']:,} |
| DPTAM-BiGRU | {dptam_row['Test_MAE']:.6f} | {dptam_row['Test_RMSE']:.6f} | {dptam_row['Test_R2']:.6f} | {dptam_row['Total_Params']:,} |

### 性能改进分析

- **RMSE改进**: {rmse_improvement:+.2f}%
- **MAE改进**: {mae_improvement:+.2f}%
- **R²改进**: {r2_improvement:+.2f}%
- **参数量增加**: {param_increase:+.2f}%

## 关键发现

1. **时序注意力效果**: {'DPTAM时序注意力机制有效提升了模型性能' if rmse_improvement > 0 else 'DPTAM时序注意力机制未能显著提升模型性能'}

2. **计算成本**: 相比基线BiGRU模型，DPTAM-BiGRU增加了{param_increase:.1f}%的参数量

3. **预测精度**: {'RMSE指标改进了' + f'{rmse_improvement:.2f}%' if rmse_improvement > 0 else 'RMSE指标下降了' + f'{abs(rmse_improvement):.2f}%'}

4. **模型复杂度**: 融合模型在保持相对较低的参数增长的同时，{'实现了性能提升' if rmse_improvement > 0 else '未能实现预期的性能提升'}

## 结论

{'DPTAM-BiGRU融合模型成功验证了时序注意力机制在风电功率预测任务中的有效性，通过为不同时间段分配差异化的注意力权重，提升了模型的预测精度。' if rmse_improvement > 0 else 'DPTAM-BiGRU融合模型在当前实验设置下未能显著超越基线BiGRU模型，可能需要进一步调优模型结构或训练策略。'}

实验时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

        # 保存报告
        report_path = os.path.join(get_current_paths()['reports'], 'dptam_bigru_comparison_report.md')
        os.makedirs(os.path.dirname(report_path), exist_ok=True)

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)

        print(f"对比实验报告已保存至: {report_path}")

    def run_comparison_experiment(self) -> None:
        """运行完整的对比实验流程"""
        print("开始DPTAM-BiGRU vs BiGRU 模型对比实验")
        print("评估DPTAM时序注意力机制对BiGRU模型性能的提升效果")
        print("=" * 60)

        try:
            # 1. 数据准备
            data = self.load_and_prepare_data()

            # 2. 训练BiGRU基线模型
            self.train_bigru_model(data)

            # 3. 训练DPTAM-BiGRU融合模型
            self.train_dptam_bigru_model(data)

            # 4. 模型性能对比
            comparison_df = self.compare_models()

            # 5. 可视化对比结果
            self.visualize_comparison(comparison_df)

            # 6. 打印详细对比
            self.print_detailed_comparison(comparison_df)

            # 7. 生成对比报告
            self.generate_comparison_report(comparison_df)

            # 找出最佳模型
            best_model = comparison_df.loc[comparison_df['Test_RMSE'].idxmin(), 'Model']
            best_rmse = comparison_df['Test_RMSE'].min()

            print(f"\n🎯 对比实验成功完成！")
            print(f"最佳模型: {best_model}")
            print(f"最佳RMSE: {best_rmse:.6f}")

            # 保存对比结果
            comparison_path = os.path.join(get_current_paths()['reports'], 'model_comparison_results.csv')
            comparison_df.to_csv(comparison_path, index=False)
            print(f"📊 对比结果已保存至: {comparison_path}")
            print(f"📁 所有结果已保存到: {self.training_paths['results_root']}")
            print(f"🕒 对比实验时间戳: {self.training_paths['timestamp']}")

        except Exception as e:
            print(f"\n❌ 对比实验过程中出现错误: {str(e)}")
            import traceback
            traceback.print_exc()

# 主程序
if __name__ == "__main__":
    comparator = ModelComparator()
    comparator.run_comparison_experiment()
