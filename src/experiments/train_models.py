"""
模型训练脚本，训练GRU和LSTM模型并进行对比
"""

import os
import sys
import numpy as np
import pandas as pd
import torch
import warnings
warnings.filterwarnings('ignore')

# 设置随机种子
torch.manual_seed(42)
np.random.seed(42)

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.data_processing.data_loader import DataLoader
from src.data_processing.preprocessor import DataPreprocessor
from src.models.gru_model import GRUModel
from src.models.lstm_model import LSTMModel
from src.utils.visualization import AcademicVisualizer
from src.utils.metrics import ModelEvaluator
from src.utils.config import DATA_PATHS, MODEL_CONFIG, setup_matplotlib

class ModelTrainer:
    """模型训练器，负责训练和对比GRU和LSTM模型"""
    
    def __init__(self):
        """初始化训练器"""
        self.data_loader = DataLoader()
        # 使用MinMaxScaler对目标变量进行标准化，以获得更小的误差指标
        self.preprocessor = DataPreprocessor(use_minmax_for_target=True)
        self.visualizer = AcademicVisualizer()
        self.evaluator = ModelEvaluator()

        self.models = {}
        self.histories = {}
        self.results = {}

        # 设置matplotlib
        setup_matplotlib()

        print("=" * 60)
        print("模型训练器已初始化")
        print("目标变量标准化方式: MinMaxScaler (0-1范围)")
        print("=" * 60)
    
    def load_and_prepare_data(self) -> dict:
        """
        加载和准备数据
        
        Returns:
            准备好的训练数据
        """
        print("=" * 60)
        print("步骤1: 数据加载和预处理")
        print("=" * 60)
        
        # 加载数据
        df = self.data_loader.load_data()

        # 显示原始数据的Power统计信息
        print(f"原始Power数据统计:")
        print(f"  最小值: {df['Power'].min():.4f}")
        print(f"  最大值: {df['Power'].max():.4f}")
        print(f"  平均值: {df['Power'].mean():.4f}")
        print(f"  标准差: {df['Power'].std():.4f}")

        # 数据分析和可视化
        self.data_loader.analyze_data()
        self.data_loader.visualize_data(save_path=DATA_PATHS['figures'])
        
        # 数据预处理
        self.preprocessor.set_data(df)
        df_processed = self.preprocessor.preprocess()
        feature_columns = self.preprocessor.select_features()
        
        # 保存预处理后的数据
        self.preprocessor.save_processed_data()
        
        # 准备训练数据
        data = self.preprocessor.prepare_data_for_training(
            sequence_length=MODEL_CONFIG['sequence_length'],
            train_ratio=MODEL_CONFIG['train_ratio'],
            val_ratio=MODEL_CONFIG['val_ratio']
        )

        # 创建PyTorch数据加载器
        train_loader, val_loader, test_loader = self.preprocessor.create_pytorch_dataloaders(
            data,
            batch_size=MODEL_CONFIG['batch_size']
        )

        # 将数据加载器添加到数据字典中
        data['train_loader'] = train_loader
        data['val_loader'] = val_loader
        data['test_loader'] = test_loader

        print(f"数据准备完成，特征数量: {data['n_features']}")
        print(f"使用的标准化器类型: {type(self.preprocessor.scaler_y).__name__}")
        return data
    
    def train_gru_model(self, data: dict) -> None:
        """
        训练GRU模型
        
        Args:
            data: 训练数据
        """
        print("\n" + "=" * 60)
        print("步骤2: 训练GRU模型")
        print("=" * 60)
        
        # 创建GRU模型
        gru_model = GRUModel(
            sequence_length=data['sequence_length'],
            n_features=data['n_features'],
            dropout_rate=MODEL_CONFIG['dropout_rate']
        )

        # 设置标准化器
        gru_model.set_scalers(
            self.preprocessor.scaler_X,
            self.preprocessor.scaler_y
        )

        # 训练模型
        history = gru_model.train_model(
            train_loader=data['train_loader'],
            val_loader=data['val_loader'],
            epochs=MODEL_CONFIG['epochs'],
            patience=MODEL_CONFIG['patience'],
            learning_rate=MODEL_CONFIG['learning_rate']
        )
        
        # 保存模型
        gru_model.save_model()
        
        # 评估模型（使用标准化后的指标）
        results = gru_model.evaluate(
            X_train=data['X_train'],
            y_train=data['y_train'],
            X_val=data['X_val'],
            y_val=data['y_val'],
            X_test=data['X_test'],
            y_test=data['y_test'],
            use_normalized_metrics=True
        )
        
        # 存储结果
        self.models['GRU'] = gru_model
        self.histories['GRU'] = history
        self.results['GRU'] = results
        
        print("GRU模型训练完成！")
    
    def train_lstm_model(self, data: dict) -> None:
        """
        训练LSTM模型
        
        Args:
            data: 训练数据
        """
        print("\n" + "=" * 60)
        print("步骤3: 训练LSTM模型")
        print("=" * 60)
        
        # 创建LSTM模型
        lstm_model = LSTMModel(
            sequence_length=data['sequence_length'],
            n_features=data['n_features'],
            dropout_rate=MODEL_CONFIG['dropout_rate']
        )

        # 设置标准化器
        lstm_model.set_scalers(
            self.preprocessor.scaler_X,
            self.preprocessor.scaler_y
        )

        # 训练模型
        history = lstm_model.train_model(
            train_loader=data['train_loader'],
            val_loader=data['val_loader'],
            epochs=MODEL_CONFIG['epochs'],
            patience=MODEL_CONFIG['patience'],
            learning_rate=MODEL_CONFIG['learning_rate']
        )
        
        # 保存模型
        lstm_model.save_model()
        
        # 评估模型（使用标准化后的指标）
        results = lstm_model.evaluate(
            X_train=data['X_train'],
            y_train=data['y_train'],
            X_val=data['X_val'],
            y_val=data['y_val'],
            X_test=data['X_test'],
            y_test=data['y_test'],
            use_normalized_metrics=True
        )
        
        # 存储结果
        self.models['LSTM'] = lstm_model
        self.histories['LSTM'] = history
        self.results['LSTM'] = results
        
        print("LSTM模型训练完成！")
    
    def compare_models(self) -> None:
        """比较模型性能"""
        print("\n" + "=" * 60)
        print("步骤4: 模型性能对比")
        print("=" * 60)
        
        # 生成训练历史对比图
        self.visualizer.plot_training_history(
            self.histories,
            save_path=DATA_PATHS['figures']
        )
        
        # 生成预测结果对比图（样本数量改为300）
        for dataset in ['train', 'val', 'test']:
            self.visualizer.plot_prediction_comparison(
                self.results,
                dataset=dataset,
                sample_size=50,
                save_path=DATA_PATHS['figures']
            )

        # 生成综合预测对比图（原始功率 vs GRU vs LSTM）
        for dataset in ['test']:  # 主要关注测试集
            self.visualizer.plot_combined_prediction_comparison(
                self.results,
                dataset=dataset,
                sample_size=50,
                save_path=DATA_PATHS['figures']
            )
        
        # 生成评估指标对比图
        self.visualizer.plot_metrics_comparison(
            self.results,
            save_path=DATA_PATHS['figures']
        )
        
        # 生成误差分析图
        self.visualizer.plot_error_analysis(
            self.results,
            dataset='test',
            save_path=DATA_PATHS['figures']
        )
        
        # 模型性能比较
        comparison_df = self.evaluator.compare_models(
            self.results,
            save_path=DATA_PATHS['reports']
        )
        
        # 找到最佳模型
        best_model, best_value = self.evaluator.find_best_model(
            self.results,
            primary_metric='RMSE',
            dataset='test'
        )
        
        return comparison_df, best_model, best_value

    def print_detailed_results(self):
        """打印详细的结果对比"""
        print("\n" + "=" * 80)
        print("详细结果对比 (使用MinMaxScaler标准化目标变量)")
        print("=" * 80)

        for model_name in ['GRU', 'LSTM']:
            print(f"\n{model_name}模型结果:")
            print("-" * 40)

            for dataset in ['train', 'val', 'test']:
                metrics = self.results[model_name][dataset]
                print(f"\n{dataset.capitalize()}集:")
                for metric, value in metrics.items():
                    if metric in ['MAPE', 'SMAPE', 'WAPE']:
                        print(f"  {metric}: {value:.4f}%")
                    else:
                        print(f"  {metric}: {value:.6f}")

        print("\n" + "=" * 80)
        print("标准化指标说明:")
        print("- 使用MinMaxScaler将目标变量标准化到0-1范围")
        print("- MAE和MSE指标显示为标准化后的结果(零点几范围)")
        print("- 这些指标更适合不同模型之间的性能比较")
        print("- 标准化后的指标消除了原始数值大小的影响")
        print("- 如需原始功率值的误差，可设置use_normalized_metrics=False")
        print("=" * 80)

    def generate_report(self, comparison_df: pd.DataFrame,
                       best_model: str, best_value: float) -> None:
        """
        生成实验报告
        
        Args:
            comparison_df: 模型比较结果
            best_model: 最佳模型名称
            best_value: 最佳模型指标值
        """
        print("\n" + "=" * 60)
        print("步骤5: 生成实验报告")
        print("=" * 60)
        
        report_content = f"""
# 风电功率预测深度学习模型对比实验报告

## 实验概述
本实验对比了GRU和LSTM两种深度学习模型在风电功率预测任务上的性能。

## 数据集信息
- 数据来源: Site_1_standardized.csv
- 特征数量: {self.models['GRU'].n_features}
- 序列长度: {self.models['GRU'].sequence_length}
- 训练集比例: {MODEL_CONFIG['train_ratio']}
- 验证集比例: {MODEL_CONFIG['val_ratio']}
- 测试集比例: {MODEL_CONFIG['test_ratio']}

## 模型配置
### GRU模型
- GRU层单元数: {self.models['GRU'].gru_units}
- 全连接层单元数: {self.models['GRU'].dense_units}
- 总参数量: {sum(p.numel() for p in self.models['GRU'].parameters()):,}

### LSTM模型
- LSTM层单元数: {self.models['LSTM'].lstm_units}
- 全连接层单元数: {self.models['LSTM'].dense_units}
- 总参数量: {sum(p.numel() for p in self.models['LSTM'].parameters()):,}

## 训练配置
- 批次大小: {MODEL_CONFIG['batch_size']}
- 最大训练轮数: {MODEL_CONFIG['epochs']}
- 早停耐心值: {MODEL_CONFIG['patience']}
- 学习率: {MODEL_CONFIG['learning_rate']}
- Dropout比例: {MODEL_CONFIG['dropout_rate']}

## 实验结果

### 测试集性能对比
"""
        
        # 添加测试集性能表格
        for model_name in ['GRU', 'LSTM']:
            test_metrics = self.results[model_name]['test']
            report_content += f"\n#### {model_name}模型\n"
            for metric, value in test_metrics.items():
                if metric in ['MAPE', 'SMAPE', 'WAPE']:
                    report_content += f"- {metric}: {value:.4f}%\n"
                else:
                    report_content += f"- {metric}: {value:.4f}\n"
        
        report_content += f"""
### 最佳模型
基于RMSE指标，最佳模型为: **{best_model}** (RMSE: {best_value:.4f})

## 结论
1. 两种模型都能有效预测风电功率
2. {best_model}模型在测试集上表现更优
3. 模型具有良好的泛化能力

## 生成的文件
- 训练好的模型: results/models/
- 可视化图表: results/figures/
- 详细数据: results/reports/
"""
        
        # 保存报告
        report_path = os.path.join(DATA_PATHS['reports'], 'experiment_report.md')
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"实验报告已保存到: {report_path}")
        
        # 打印总结
        print("\n" + "=" * 60)
        print("实验完成总结")
        print("=" * 60)
        print(f"最佳模型: {best_model}")
        print(f"测试集RMSE: {best_value:.4f}")
        print(f"生成的图表数量: 多个可视化图表")
        print(f"保存的模型数量: 2个 (GRU + LSTM)")
        print("=" * 60)
    
    def run_experiment(self) -> None:
        """运行完整的实验流程"""
        print("开始风电功率预测深度学习模型对比实验")
        print("使用MinMaxScaler对目标变量进行标准化，确保MAE/MSE为归一化结果")
        print("=" * 60)
        
        try:
            # 1. 数据准备
            data = self.load_and_prepare_data()
            
            # 2. 训练GRU模型
            self.train_gru_model(data)
            
            # 3. 训练LSTM模型
            self.train_lstm_model(data)
            
            # 4. 模型对比
            comparison_df, best_model, best_value = self.compare_models()

            # 5. 打印详细结果
            self.print_detailed_results()

            # 6. 生成报告
            self.generate_report(comparison_df, best_model, best_value)

            print("\n实验成功完成！")
            print(f"最佳模型: {best_model} (RMSE: {best_value:.6f})")
            
        except Exception as e:
            print(f"\n实验过程中出现错误: {str(e)}")
            import traceback
            traceback.print_exc()

# 主程序
if __name__ == "__main__":
    trainer = ModelTrainer()
    trainer.run_experiment()
