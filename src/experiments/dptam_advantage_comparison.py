"""
DPTAM优势对比分析实验
对比分析基线BiGRU、DPTAM-BiGRU和ASB-DPTAM-BiGRU模型的性能差异
验证DPTAM时序注意力机制和ASB频域增强的有效性
"""

import os
import sys
import torch
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.data_processing.data_loader import DataLoader
from src.data_processing.preprocessor import DataPreprocessor
from src.models.bigru_model import BiGRUModel
from src.models.dptam_bigru_model import DPTAMBiGRUModel
from src.models.asb_dptam_bigru_model import ASBDPTAMBiGRUModel
from src.utils.visualization_manager import auto_visualize
from src.utils.metrics import ModelEvaluator
from src.utils.config import (
    MODEL_CONFIG, DATASET_CONFIG, BIGRU_CONFIG, DPTAM_BIGRU_CONFIG, ASB_DPTAM_BIGRU_CONFIG,
    setup_matplotlib, setup_training_session, get_current_paths
)


class DPTAMAdvantageComparator:
    """DPTAM优势对比分析实验类"""
    
    def __init__(self):
        """初始化对比实验"""
        self.models = {}
        self.results = {}
        self.histories = {}
        self.data_loader = None
        self.preprocessor = None
        
        # 设置matplotlib和训练会话
        setup_matplotlib()
        setup_training_session('DPTAM_Advantage_Comparison')
        
        print("=" * 80)
        print("DPTAM优势对比分析实验")
        print("=" * 80)
        print("实验目标:")
        print("• 验证DPTAM时序注意力机制的有效性")
        print("• 验证ASB频域增强的额外价值")
        print("• 对比三种模型的性能差异")
        print("=" * 80)

    def load_and_prepare_data(self) -> dict:
        """加载和预处理数据"""
        print("\n步骤1: 数据加载与预处理")
        print("-" * 40)
        
        # 数据加载
        self.data_loader = DataLoader()
        raw_data = self.data_loader.load_data()
        
        print(f"原始数据形状: {raw_data.shape}")

        # 显示原始数据的Power统计信息
        print(f"原始Power数据统计:")
        print(f"  最小值: {raw_data[DATASET_CONFIG['target_column']].min():.4f}")
        print(f"  最大值: {raw_data[DATASET_CONFIG['target_column']].max():.4f}")
        print(f"  平均值: {raw_data[DATASET_CONFIG['target_column']].mean():.4f}")
        print(f"  标准差: {raw_data[DATASET_CONFIG['target_column']].std():.4f}")

        # 数据分析（跳过可视化以避免阻塞）
        self.data_loader.analyze_data()

        # 数据预处理
        self.preprocessor = DataPreprocessor()
        self.preprocessor.set_data(raw_data)
        df_processed = self.preprocessor.preprocess()
        feature_columns = self.preprocessor.select_features()

        # 保存预处理后的数据
        self.preprocessor.save_processed_data()

        # 准备训练数据
        processed_data = self.preprocessor.prepare_data_for_training(
            sequence_length=MODEL_CONFIG['sequence_length'],
            train_ratio=MODEL_CONFIG['train_ratio'],
            val_ratio=MODEL_CONFIG['val_ratio']
        )

        # 创建PyTorch数据加载器
        train_loader, val_loader, test_loader = self.preprocessor.create_pytorch_dataloaders(
            processed_data,
            batch_size=MODEL_CONFIG['batch_size']
        )

        # 将数据加载器添加到数据字典中
        processed_data['train_loader'] = train_loader
        processed_data['val_loader'] = val_loader
        processed_data['test_loader'] = test_loader
        
        print(f"数据准备完成，特征数量: {processed_data['n_features']}")
        print(f"使用的标准化器类型: {type(self.preprocessor.scaler_y).__name__}")
        print(f"序列长度: {processed_data['sequence_length']}")
        print(f"训练集大小: {len(processed_data['train_loader'].dataset)}")
        print(f"验证集大小: {len(processed_data['val_loader'].dataset)}")
        print(f"测试集大小: {len(processed_data['test_loader'].dataset)}")

        return processed_data

    def train_baseline_bigru(self, data: dict) -> None:
        """训练基线BiGRU模型"""
        print("\n步骤2: 训练基线BiGRU模型")
        print("-" * 40)
        
        # 创建基线BiGRU模型
        self.models['Baseline-BiGRU'] = BiGRUModel(
            sequence_length=data['sequence_length'],
            n_features=data['n_features'],
            bigru_units=BIGRU_CONFIG['bigru_units'],
            dense_units=BIGRU_CONFIG['dense_units'],
            dropout_rate=BIGRU_CONFIG['dropout_rate']
        )
        
        # 设置标准化器
        self.models['Baseline-BiGRU'].set_scalers(
            self.preprocessor.scaler_X,
            self.preprocessor.scaler_y
        )
        
        # 训练模型
        self.histories['Baseline-BiGRU'] = self.models['Baseline-BiGRU'].train_model(
            train_loader=data['train_loader'],
            val_loader=data['val_loader'],
            epochs=MODEL_CONFIG['epochs'],
            patience=MODEL_CONFIG['patience'],
            learning_rate=MODEL_CONFIG['learning_rate']
        )
        
        # 评估模型
        self.results['Baseline-BiGRU'] = self.models['Baseline-BiGRU'].evaluate(
            X_train=data['X_train'],
            y_train=data['y_train'],
            X_val=data['X_val'],
            y_val=data['y_val'],
            X_test=data['X_test'],
            y_test=data['y_test'],
            use_normalized_metrics=True
        )
        
        baseline_info = self.models['Baseline-BiGRU'].get_model_info()
        print(f"✅ 基线BiGRU训练完成")
        print(f"   参数量: {baseline_info['total_params']:,}")
        print(f"   测试RMSE: {self.results['Baseline-BiGRU']['test']['RMSE']:.6f}")

    def train_dptam_bigru(self, data: dict) -> None:
        """训练DPTAM-BiGRU模型"""
        print("\n步骤3: 训练DPTAM-BiGRU模型")
        print("-" * 40)
        
        # 创建DPTAM-BiGRU模型
        self.models['DPTAM-BiGRU'] = DPTAMBiGRUModel(
            sequence_length=data['sequence_length'],
            n_features=data['n_features'],
            n_segment=DPTAM_BIGRU_CONFIG['n_segment'],
            dptam_kernel_size=DPTAM_BIGRU_CONFIG['dptam_kernel_size'],
            bigru_units=DPTAM_BIGRU_CONFIG['bigru_units'],
            dense_units=DPTAM_BIGRU_CONFIG['dense_units'],
            dropout_rate=DPTAM_BIGRU_CONFIG['dropout_rate']
        )
        
        # 设置标准化器
        self.models['DPTAM-BiGRU'].set_scalers(
            self.preprocessor.scaler_X,
            self.preprocessor.scaler_y
        )
        
        # 训练模型
        self.histories['DPTAM-BiGRU'] = self.models['DPTAM-BiGRU'].train_model(
            train_loader=data['train_loader'],
            val_loader=data['val_loader'],
            epochs=MODEL_CONFIG['epochs'],
            patience=MODEL_CONFIG['patience'],
            learning_rate=MODEL_CONFIG['learning_rate']
        )
        
        # 评估模型
        self.results['DPTAM-BiGRU'] = self.models['DPTAM-BiGRU'].evaluate(
            X_train=data['X_train'],
            y_train=data['y_train'],
            X_val=data['X_val'],
            y_val=data['y_val'],
            X_test=data['X_test'],
            y_test=data['y_test'],
            use_normalized_metrics=True
        )
        
        dptam_info = self.models['DPTAM-BiGRU'].get_model_info()
        print(f"✅ DPTAM-BiGRU训练完成")
        print(f"   参数量: {dptam_info['total_params']:,}")
        print(f"   测试RMSE: {self.results['DPTAM-BiGRU']['test']['RMSE']:.6f}")

    def train_asb_dptam_bigru(self, data: dict) -> None:
        """训练ASB-DPTAM-BiGRU模型"""
        print("\n步骤4: 训练ASB-DPTAM-BiGRU模型")
        print("-" * 40)
        
        # 创建ASB-DPTAM-BiGRU模型
        self.models['ASB-DPTAM-BiGRU'] = ASBDPTAMBiGRUModel(
            sequence_length=data['sequence_length'],
            n_features=data['n_features'],
            n_segment=ASB_DPTAM_BIGRU_CONFIG['n_segment'],
            dptam_kernel_size=ASB_DPTAM_BIGRU_CONFIG['dptam_kernel_size'],
            bigru_units=ASB_DPTAM_BIGRU_CONFIG['bigru_units'],
            dense_units=ASB_DPTAM_BIGRU_CONFIG['dense_units'],
            dropout_rate=ASB_DPTAM_BIGRU_CONFIG['dropout_rate'],
            asb_adaptive_filter=ASB_DPTAM_BIGRU_CONFIG['asb_adaptive_filter']
        )
        
        # 设置标准化器
        self.models['ASB-DPTAM-BiGRU'].set_scalers(
            self.preprocessor.scaler_X,
            self.preprocessor.scaler_y
        )
        
        # 训练模型
        self.histories['ASB-DPTAM-BiGRU'] = self.models['ASB-DPTAM-BiGRU'].train_model(
            train_loader=data['train_loader'],
            val_loader=data['val_loader'],
            epochs=MODEL_CONFIG['epochs'],
            patience=MODEL_CONFIG['patience'],
            learning_rate=MODEL_CONFIG['learning_rate']
        )
        
        # 评估模型
        self.results['ASB-DPTAM-BiGRU'] = self.models['ASB-DPTAM-BiGRU'].evaluate(
            X_train=data['X_train'],
            y_train=data['y_train'],
            X_val=data['X_val'],
            y_val=data['y_val'],
            X_test=data['X_test'],
            y_test=data['y_test'],
            use_normalized_metrics=True
        )
        
        asb_info = self.models['ASB-DPTAM-BiGRU'].get_model_info()
        print(f"✅ ASB-DPTAM-BiGRU训练完成")
        print(f"   参数量: {asb_info['total_params']:,}")
        print(f"   测试RMSE: {self.results['ASB-DPTAM-BiGRU']['test']['RMSE']:.6f}")

    def analyze_advantages(self) -> None:
        """分析各模型的优势"""
        print("\n步骤5: 模型优势对比分析")
        print("-" * 40)
        
        # 获取基线性能
        baseline_rmse = self.results['Baseline-BiGRU']['test']['RMSE']
        baseline_r2 = self.results['Baseline-BiGRU']['test']['R2']
        baseline_mae = self.results['Baseline-BiGRU']['test']['MAE']
        
        print(f"📊 性能对比分析:")
        print(f"基线BiGRU:")
        print(f"  RMSE: {baseline_rmse:.6f}")
        print(f"  R²:   {baseline_r2:.6f}")
        print(f"  MAE:  {baseline_mae:.6f}")
        
        # DPTAM vs Baseline
        if 'DPTAM-BiGRU' in self.results:
            dptam_rmse = self.results['DPTAM-BiGRU']['test']['RMSE']
            dptam_r2 = self.results['DPTAM-BiGRU']['test']['R2']
            dptam_mae = self.results['DPTAM-BiGRU']['test']['MAE']
            
            dptam_rmse_improvement = ((baseline_rmse - dptam_rmse) / baseline_rmse) * 100
            dptam_r2_improvement = ((dptam_r2 - baseline_r2) / baseline_r2) * 100
            dptam_mae_improvement = ((baseline_mae - dptam_mae) / baseline_mae) * 100
            
            print(f"\nDPTAM-BiGRU:")
            print(f"  RMSE: {dptam_rmse:.6f} (改进: {dptam_rmse_improvement:+.2f}%)")
            print(f"  R²:   {dptam_r2:.6f} (改进: {dptam_r2_improvement:+.2f}%)")
            print(f"  MAE:  {dptam_mae:.6f} (改进: {dptam_mae_improvement:+.2f}%)")
        
        # ASB-DPTAM vs Baseline and DPTAM
        if 'ASB-DPTAM-BiGRU' in self.results:
            asb_rmse = self.results['ASB-DPTAM-BiGRU']['test']['RMSE']
            asb_r2 = self.results['ASB-DPTAM-BiGRU']['test']['R2']
            asb_mae = self.results['ASB-DPTAM-BiGRU']['test']['MAE']
            
            asb_rmse_improvement = ((baseline_rmse - asb_rmse) / baseline_rmse) * 100
            asb_r2_improvement = ((asb_r2 - baseline_r2) / baseline_r2) * 100
            asb_mae_improvement = ((baseline_mae - asb_mae) / baseline_mae) * 100
            
            print(f"\nASB-DPTAM-BiGRU:")
            print(f"  RMSE: {asb_rmse:.6f} (改进: {asb_rmse_improvement:+.2f}%)")
            print(f"  R²:   {asb_r2:.6f} (改进: {asb_r2_improvement:+.2f}%)")
            print(f"  MAE:  {asb_mae:.6f} (改进: {asb_mae_improvement:+.2f}%)")
            
            # ASB相对于DPTAM的额外改进
            if 'DPTAM-BiGRU' in self.results:
                asb_vs_dptam_rmse = ((dptam_rmse - asb_rmse) / dptam_rmse) * 100
                asb_vs_dptam_r2 = ((asb_r2 - dptam_r2) / dptam_r2) * 100
                asb_vs_dptam_mae = ((dptam_mae - asb_mae) / dptam_mae) * 100
                
                print(f"\n🔥 ASB相对DPTAM的额外改进:")
                print(f"  RMSE: {asb_vs_dptam_rmse:+.2f}%")
                print(f"  R²:   {asb_vs_dptam_r2:+.2f}%")
                print(f"  MAE:  {asb_vs_dptam_mae:+.2f}%")
                
                if asb_vs_dptam_rmse > 0:
                    print(f"  ✅ ASB成功进一步提升了DPTAM性能！")
                    print(f"  💡 频域增强确实有效！")
                else:
                    print(f"  ⚠️ ASB未能进一步提升DPTAM性能")

    def generate_comparison_report(self) -> None:
        """生成对比报告"""
        print("\n步骤6: 生成对比报告")
        print("-" * 40)
        
        # 计算改进指标
        baseline_rmse = self.results['Baseline-BiGRU']['test']['RMSE']
        
        improvements = {}
        for model_name in ['DPTAM-BiGRU', 'ASB-DPTAM-BiGRU']:
            if model_name in self.results:
                model_rmse = self.results[model_name]['test']['RMSE']
                improvement = ((baseline_rmse - model_rmse) / baseline_rmse) * 100
                improvements[model_name] = improvement
        
        # 生成报告内容
        report_content = f"""# DPTAM优势对比分析报告

## 实验概述
本实验对比了三种风电功率预测模型的性能：
1. **Baseline-BiGRU**: 基线双向GRU模型
2. **DPTAM-BiGRU**: 加入DPTAM时序注意力机制
3. **ASB-DPTAM-BiGRU**: 加入ASB频域增强的串联架构

## 模型配置
- **序列长度**: {MODEL_CONFIG['sequence_length']}
- **训练轮数**: {MODEL_CONFIG['epochs']}
- **学习率**: {MODEL_CONFIG['learning_rate']}
- **批次大小**: {MODEL_CONFIG['batch_size']}

## 性能对比结果

### 测试集性能指标
| 模型 | RMSE | R² | MAE | 参数量 |
|------|------|----|----|--------|
| Baseline-BiGRU | {self.results['Baseline-BiGRU']['test']['RMSE']:.6f} | {self.results['Baseline-BiGRU']['test']['R2']:.6f} | {self.results['Baseline-BiGRU']['test']['MAE']:.6f} | {self.models['Baseline-BiGRU'].get_model_info()['total_params']:,} |
"""
        
        if 'DPTAM-BiGRU' in self.results:
            report_content += f"| DPTAM-BiGRU | {self.results['DPTAM-BiGRU']['test']['RMSE']:.6f} | {self.results['DPTAM-BiGRU']['test']['R2']:.6f} | {self.results['DPTAM-BiGRU']['test']['MAE']:.6f} | {self.models['DPTAM-BiGRU'].get_model_info()['total_params']:,} |\n"
        
        if 'ASB-DPTAM-BiGRU' in self.results:
            report_content += f"| ASB-DPTAM-BiGRU | {self.results['ASB-DPTAM-BiGRU']['test']['RMSE']:.6f} | {self.results['ASB-DPTAM-BiGRU']['test']['R2']:.6f} | {self.results['ASB-DPTAM-BiGRU']['test']['MAE']:.6f} | {self.models['ASB-DPTAM-BiGRU'].get_model_info()['total_params']:,} |\n"
        
        report_content += f"""
### 性能改进分析
"""
        
        for model_name, improvement in improvements.items():
            status = "✅ 有效提升" if improvement > 0 else "⚠️ 未能提升"
            report_content += f"- **{model_name}**: {improvement:+.2f}% {status}\n"
        
        report_content += f"""
## 结论

### DPTAM时序注意力机制
{'✅ **验证成功**: DPTAM时序注意力机制有效提升了预测性能' if improvements.get('DPTAM-BiGRU', 0) > 0 else '❌ **验证失败**: DPTAM在当前配置下未能提升性能'}

### ASB频域增强
{'✅ **验证成功**: ASB频域增强进一步提升了模型性能' if improvements.get('ASB-DPTAM-BiGRU', 0) > improvements.get('DPTAM-BiGRU', 0) else '⚠️ **需要优化**: ASB频域增强效果有限，可能需要调优'}

### 技术洞察
- **时序注意力**: {'有效识别重要时间段' if improvements.get('DPTAM-BiGRU', 0) > 0 else '可能需要调整分段策略'}
- **频域处理**: {'有效减少噪声干扰' if improvements.get('ASB-DPTAM-BiGRU', 0) > 0 else '可能数据特性不适合频域处理'}
- **串联架构**: {'渐进式处理策略有效' if improvements.get('ASB-DPTAM-BiGRU', 0) > 0 else '可能需要优化处理顺序'}

实验时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        # 保存报告
        report_path = os.path.join(get_current_paths()['reports'], 'dptam_advantage_comparison_report.md')
        os.makedirs(os.path.dirname(report_path), exist_ok=True)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"✅ 对比报告已保存至: {report_path}")

    def run_comparison_experiment(self) -> None:
        """运行完整的对比实验"""
        try:
            # 1. 数据准备
            data = self.load_and_prepare_data()
            
            # 2. 训练基线模型
            self.train_baseline_bigru(data)
            
            # 3. 训练DPTAM模型
            self.train_dptam_bigru(data)
            
            # 4. 训练ASB-DPTAM模型
            self.train_asb_dptam_bigru(data)
            
            # 5. 分析优势
            self.analyze_advantages()
            
            # 6. 生成报告
            self.generate_comparison_report()
            
            print(f"\n🎉 DPTAM优势对比实验完成！")
            print(f"📁 所有结果已保存到: {get_current_paths()['results_root']}")
            
        except Exception as e:
            print(f"\n❌ 实验过程中发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
            raise


def main():
    """主函数"""
    comparator = DPTAMAdvantageComparator()
    comparator.run_comparison_experiment()


if __name__ == "__main__":
    main()
