"""
DPTAM-BiGRU融合模型训练脚本
结合DPTAM时序注意力机制和BiGRU双向特征提取的风电功率预测模型
"""

import os
import sys
import numpy as np
import pandas as pd
import torch
import warnings
warnings.filterwarnings('ignore')

# 设置随机种子
torch.manual_seed(42)
np.random.seed(42)

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.data_processing.data_loader import DataLoader
from src.data_processing.preprocessor import DataPreprocessor
from src.models.dptam_bigru_model import DPTAMBiGRUModel
from src.utils.visualization_manager import auto_visualize
from src.utils.metrics import ModelEvaluator
from src.utils.config import (
    MODEL_CONFIG, DATASET_CONFIG, DPTAM_BIGRU_CONFIG, setup_matplotlib,
    setup_training_session, get_current_paths
)

class DPTAMBiGRUTrainer:
    """DPTAM-BiGRU融合模型训练器"""
    
    def __init__(self):
        """初始化训练器"""
        # 设置时间戳训练会话
        self.training_paths = setup_training_session()
        print(f"🕒 训练会话时间戳: {self.training_paths['timestamp']}")
        print(f"📁 结果保存路径: {self.training_paths['results_root']}")

        self.data_loader = DataLoader()
        self.preprocessor = DataPreprocessor()
        self.visualizer = AcademicVisualizer()
        self.evaluator = ModelEvaluator()
        self.model = None
        self.history = None
        self.results = None

        # 设置matplotlib
        setup_matplotlib()

        print("✅ DPTAM-BiGRU融合模型训练器初始化完成")
    
    def load_and_prepare_data(self) -> dict:
        """
        加载和准备数据
        
        Returns:
            准备好的训练数据
        """
        print("\n" + "=" * 60)
        print("步骤1: 数据加载和预处理")
        print("=" * 60)
        
        # 加载数据
        df = self.data_loader.load_data()
        
        # 显示原始数据的Power统计信息
        print(f"原始Power数据统计:")
        print(f"  最小值: {df['Power'].min():.4f}")
        print(f"  最大值: {df['Power'].max():.4f}")
        print(f"  平均值: {df['Power'].mean():.4f}")
        print(f"  标准差: {df['Power'].std():.4f}")
        
        # 数据分析和可视化
        self.data_loader.analyze_data()
        self.data_loader.visualize_data(save_path=get_current_paths()['figures'])
        
        # 数据预处理
        self.preprocessor.set_data(df)
        df_processed = self.preprocessor.preprocess()
        feature_columns = self.preprocessor.select_features()
        
        # 保存预处理后的数据
        self.preprocessor.save_processed_data()
        
        # 准备训练数据
        data = self.preprocessor.prepare_data_for_training(
            sequence_length=MODEL_CONFIG['sequence_length'],
            train_ratio=MODEL_CONFIG['train_ratio'],
            val_ratio=MODEL_CONFIG['val_ratio']
        )

        # 创建PyTorch数据加载器
        train_loader, val_loader, test_loader = self.preprocessor.create_pytorch_dataloaders(
            data,
            batch_size=MODEL_CONFIG['batch_size']
        )

        # 将数据加载器添加到数据字典中
        data['train_loader'] = train_loader
        data['val_loader'] = val_loader
        data['test_loader'] = test_loader

        print(f"数据准备完成，特征数量: {data['n_features']}")
        print(f"使用的标准化器类型: {type(self.preprocessor.scaler_y).__name__}")
        return data
    
    def train_dptam_bigru_model(self, data: dict) -> None:
        """
        训练DPTAM-BiGRU融合模型
        
        Args:
            data: 训练数据
        """
        print("\n" + "=" * 60)
        print("步骤2: 训练DPTAM-BiGRU融合模型")
        print("=" * 60)
        
        # 创建DPTAM-BiGRU融合模型（使用config.py中的配置）
        self.model = DPTAMBiGRUModel(
            sequence_length=data['sequence_length'],
            n_features=data['n_features'],
            n_segment=DPTAM_BIGRU_CONFIG['n_segment'],
            dptam_kernel_size=DPTAM_BIGRU_CONFIG['dptam_kernel_size'],
            bigru_units=DPTAM_BIGRU_CONFIG['bigru_units'],
            dense_units=DPTAM_BIGRU_CONFIG['dense_units'],
            dropout_rate=DPTAM_BIGRU_CONFIG['dropout_rate']
        )

        # 设置标准化器
        self.model.set_scalers(
            self.preprocessor.scaler_X,
            self.preprocessor.scaler_y
        )

        # 训练模型
        self.history = self.model.train_model(
            train_loader=data['train_loader'],
            val_loader=data['val_loader'],
            epochs=MODEL_CONFIG['epochs'],
            patience=MODEL_CONFIG['patience'],
            learning_rate=MODEL_CONFIG['learning_rate']
        )
        
        # 保存模型
        self.model.save_model()
        
        # 评估模型（使用标准化后的指标）
        self.results = self.model.evaluate(
            X_train=data['X_train'],
            y_train=data['y_train'],
            X_val=data['X_val'],
            y_val=data['y_val'],
            X_test=data['X_test'],
            y_test=data['y_test'],
            use_normalized_metrics=True
        )
        
        print("DPTAM-BiGRU融合模型训练完成！")
    
    def visualize_results(self) -> None:
        """可视化训练结果 - 使用通用可视化系统"""
        print("\n" + "=" * 60)
        print("步骤3: 结果可视化")
        print("=" * 60)

        if self.model is None or self.history is None:
            print("模型未训练，跳过可视化")
            return

        # 使用新的通用可视化系统
        from src.utils.visualization_manager import visualize_single

        generated_files = visualize_single(
            model_name='DPTAM-BiGRU',
            results=self.results,
            history=self.history,
            experiment_name='DPTAM_BiGRU',
            save_path=get_current_paths()['figures']
        )

        print(f"✅ 通用可视化系统生成了 {len(generated_files)} 个图表文件")
        print("📋 生成的图表:")
        for i, file_path in enumerate(generated_files, 1):
            filename = os.path.basename(file_path)
            print(f"  {i:2d}. {filename}")

        print("\n结果可视化完成")
    
    def analyze_attention_weights(self, data: dict) -> None:
        """
        分析DPTAM注意力权重
        
        Args:
            data: 测试数据
        """
        print("\n" + "=" * 60)
        print("步骤4: DPTAM注意力权重分析")
        print("=" * 60)
        
        if self.model is None:
            print("模型未训练，跳过注意力分析")
            return
        
        # 获取测试数据的注意力权重
        X_test = torch.tensor(data['X_test'], dtype=torch.float32)
        
        self.model.eval()
        with torch.no_grad():
            attention_weights = self.model.get_attention_weights(X_test[:100])  # 分析前100个样本
        
        # 计算平均注意力权重
        avg_attention = attention_weights.mean(dim=0).cpu().numpy()
        std_attention = attention_weights.std(dim=0).cpu().numpy()
        
        print("DPTAM时序注意力权重分析:")
        print(f"  时间段数: {len(avg_attention)}")
        
        for i, (avg, std) in enumerate(zip(avg_attention, std_attention)):
            time_range = f"{i*6}-{(i+1)*6}小时前"
            print(f"  段{i+1} ({time_range}): 平均权重={avg:.4f} ± {std:.4f}")
        
        # 找出最重要的时间段
        most_important = np.argmax(avg_attention)
        least_important = np.argmin(avg_attention)
        
        print(f"\n关键发现:")
        print(f"  最重要时间段: 段{most_important+1} (权重={avg_attention[most_important]:.4f})")
        print(f"  最不重要时间段: 段{least_important+1} (权重={avg_attention[least_important]:.4f})")
        print(f"  权重差异: {avg_attention[most_important] - avg_attention[least_important]:.4f}")
        
        # 可视化注意力权重分布
        import matplotlib.pyplot as plt
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        # 平均注意力权重
        time_labels = [f"段{i+1}\n({i*6}-{(i+1)*6}h前)" for i in range(len(avg_attention))]
        bars = ax1.bar(time_labels, avg_attention, yerr=std_attention, capsize=5, 
                      color='skyblue', edgecolor='navy', alpha=0.7)
        ax1.set_title('DPTAM时序注意力权重分布')
        ax1.set_ylabel('注意力权重')
        ax1.set_ylim(0, max(avg_attention) * 1.2)
        
        # 添加数值标签
        for bar, avg in zip(bars, avg_attention):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{avg:.3f}', ha='center', va='bottom')
        
        # 注意力权重热力图
        attention_sample = attention_weights[:50].cpu().numpy()  # 前50个样本
        im = ax2.imshow(attention_sample.T, cmap='YlOrRd', aspect='auto')
        ax2.set_title('注意力权重热力图 (前50个样本)')
        ax2.set_xlabel('样本索引')
        ax2.set_ylabel('时间段')
        ax2.set_yticks(range(len(avg_attention)))
        ax2.set_yticklabels([f"段{i+1}" for i in range(len(avg_attention))])
        
        plt.colorbar(im, ax=ax2, label='注意力权重')
        plt.tight_layout()
        
        # 保存图像
        save_path = os.path.join(get_current_paths()['figures'], 'dptam_attention_analysis.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"注意力权重分析图已保存至: {save_path}")
    
    def print_detailed_results(self) -> None:
        """打印详细的训练结果"""
        print("\n" + "=" * 60)
        print("步骤5: 详细结果报告")
        print("=" * 60)
        
        if self.results is None:
            print("没有可用的结果数据")
            return
        
        # 打印模型信息
        model_info = self.model.get_model_info()
        print("模型配置信息:")
        for key, value in model_info.items():
            print(f"  {key}: {value}")
        
        print(f"\n性能指标 (标准化数据):")
        
        # 训练集结果
        print(f"\n训练集:")
        for metric, value in self.results['train'].items():
            print(f"  {metric}: {value:.6f}")
        
        # 验证集结果
        print(f"\n验证集:")
        for metric, value in self.results['val'].items():
            print(f"  {metric}: {value:.6f}")
        
        # 测试集结果
        print(f"\n测试集:")
        for metric, value in self.results['test'].items():
            print(f"  {metric}: {value:.6f}")
        
        # 训练信息
        if self.history:
            print(f"\n训练信息:")
            print(f"  训练轮数: {len(self.history['train_loss'])}")
            print(f"  最佳验证损失: {min(self.history['val_loss']):.6f}")
            print(f"  最终训练损失: {self.history['train_loss'][-1]:.6f}")
            print(f"  最终验证损失: {self.history['val_loss'][-1]:.6f}")

    def generate_report(self) -> None:
        """生成实验报告"""
        print("\n" + "=" * 60)
        print("步骤6: 生成实验报告")
        print("=" * 60)

        if self.model is None or self.results is None:
            print("模型或结果数据不完整，无法生成报告")
            return

        # 获取模型信息
        model_info = self.model.get_model_info()

        # 生成报告内容
        report_content = f"""
# DPTAM-BiGRU融合模型风电功率预测实验报告

## 实验概述
本实验实现了DPTAM时序注意力机制与BiGRU双向特征提取的串行融合架构，用于风电功率预测任务。

## 模型架构
- **模型名称**: {model_info['model_name']}
- **融合策略**: 串行融合 (DPTAM → BiGRU → 全连接层)
- **序列长度**: {model_info['sequence_length']}
- **特征数量**: {model_info['n_features']}
- **DPTAM分段数**: {model_info['n_segment']}
- **每段长度**: {model_info['segment_length']}

## 网络配置
### DPTAM模块
- 分段数: {model_info['n_segment']}
- 卷积核大小: {model_info['dptam_kernel_size']}
- 功能: 时序注意力权重生成

### BiGRU模块
- GRU层单元数: {model_info['bigru_units']}
- 双向处理: {model_info['bidirectional']}
- 功能: 双向时序特征提取

### 全连接层
- 层单元数: {model_info['dense_units']}
- Dropout率: {model_info['dropout_rate']}
- 总参数量: {model_info['total_params']:,}

## 训练配置
- 批次大小: {MODEL_CONFIG['batch_size']}
- 最大训练轮数: {MODEL_CONFIG['epochs']}
- 早停耐心值: {MODEL_CONFIG['patience']}
- 学习率: {MODEL_CONFIG['learning_rate']}
- 数据划分: 训练{MODEL_CONFIG['train_ratio']:.1f} / 验证{MODEL_CONFIG['val_ratio']:.1f} / 测试{MODEL_CONFIG['test_ratio']:.1f}

## 实验结果

### 测试集性能指标
"""

        # 添加测试集结果
        for metric, value in self.results['test'].items():
            report_content += f"- **{metric}**: {value:.6f}\n"

        report_content += f"""
### 训练过程
- 实际训练轮数: {len(self.history['train_loss'])}
- 最佳验证损失: {min(self.history['val_loss']):.6f}
- 最终训练损失: {self.history['train_loss'][-1]:.6f}
- 最终验证损失: {self.history['val_loss'][-1]:.6f}

## 模型特点
1. **时序注意力机制**: DPTAM模块能够自动识别重要的时间段，为不同时间窗口分配不同的注意力权重
2. **双向特征提取**: BiGRU模块同时捕获前向和后向的时序依赖关系
3. **串行融合**: 先通过DPTAM增强时序特征，再通过BiGRU进行深度特征提取
4. **参数效率**: 总参数量{model_info['total_params']:,}，在保证性能的同时控制了模型复杂度

## 结论
DPTAM-BiGRU融合模型成功结合了时序注意力机制和双向循环神经网络的优势，在风电功率预测任务上取得了良好的性能表现。

实验时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

        # 保存报告
        report_path = os.path.join(get_current_paths()['reports'], 'dptam_bigru_experiment_report.md')
        os.makedirs(os.path.dirname(report_path), exist_ok=True)

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)

        print(f"实验报告已保存至: {report_path}")

    def run_experiment(self) -> None:
        """运行完整的实验流程"""
        print("开始DPTAM-BiGRU融合模型风电功率预测实验")
        print("使用MinMaxScaler对目标变量进行标准化，确保MAE/MSE为归一化结果")
        print("=" * 60)

        try:
            # 1. 数据准备
            data = self.load_and_prepare_data()

            # 2. 训练DPTAM-BiGRU融合模型
            self.train_dptam_bigru_model(data)

            # 3. 可视化结果
            self.visualize_results()

            # 4. 分析注意力权重
            self.analyze_attention_weights(data)

            # 5. 打印详细结果
            self.print_detailed_results()

            # 6. 生成报告
            self.generate_report()

            # 找到最佳指标
            test_rmse = self.results['test']['RMSE']

            print("\n✅ DPTAM-BiGRU融合模型实验成功完成！")
            print(f"🏆 测试集RMSE: {test_rmse:.6f}")
            print(f"📊 模型总参数量: {self.model.get_model_info()['total_params']:,}")
            print(f"📁 所有结果已保存到: {self.training_paths['results_root']}")
            print(f"🕒 训练会话时间戳: {self.training_paths['timestamp']}")

        except Exception as e:
            print(f"\n实验过程中出现错误: {str(e)}")
            import traceback
            traceback.print_exc()

# 主程序
if __name__ == "__main__":
    trainer = DPTAMBiGRUTrainer()
    trainer.run_experiment()
