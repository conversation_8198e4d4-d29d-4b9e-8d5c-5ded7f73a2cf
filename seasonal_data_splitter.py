#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
季节数据划分工具
自动将风电数据集按照季节进行划分
输入：完整数据集
输出：春季、夏季、秋季、冬季数据集
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime
import warnings

warnings.filterwarnings('ignore')

class SeasonalDataSplitter:
    """季节数据划分器"""
    
    def __init__(self, input_file: str, output_dir: str = "seasonal_datasets"):
        """
        初始化季节数据划分器
        
        Args:
            input_file: 输入数据文件路径
            output_dir: 输出目录
        """
        self.input_file = input_file
        self.output_dir = output_dir
        self.df = None
        self.time_col = None
        
        # 季节定义（北半球）
        self.seasons = {
            'Spring': [3, 4, 5],      # 春季：3-5月
            'Summer': [6, 7, 8],      # 夏季：6-8月
            'Autumn': [9, 10, 11],    # 秋季：9-11月
            'Winter': [12, 1, 2]      # 冬季：12-2月
        }
        
        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
        
        print(f"🌱 季节数据划分器初始化完成")
        print(f"📁 输入文件: {self.input_file}")
        print(f"📂 输出目录: {self.output_dir}")
    
    def load_data(self):
        """加载数据"""
        try:
            print(f"\n📖 正在加载数据...")
            
            # 尝试多种编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1']
            for encoding in encodings:
                try:
                    self.df = pd.read_csv(self.input_file, encoding=encoding)
                    print(f"✅ 使用 {encoding} 编码成功加载数据")
                    break
                except UnicodeDecodeError:
                    continue
            
            if self.df is None:
                raise ValueError("无法使用任何编码加载数据")
            
            print(f"📊 数据形状: {self.df.shape}")
            print(f"📋 数据列: {list(self.df.columns)}")
            
            # 识别时间列
            self._identify_time_column()
            
            # 处理时间列
            self._process_time_column()
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {str(e)}")
            return False
    
    def _identify_time_column(self):
        """识别时间列"""
        time_keywords = ['time', 'date', 'datetime', '时间', '日期']
        
        for col in self.df.columns:
            if any(keyword in col.lower() for keyword in time_keywords):
                self.time_col = col
                print(f"🕐 识别到时间列: {self.time_col}")
                return
        
        print("⚠️ 未找到时间列，请手动指定")
        # 显示所有列供用户选择
        print("可用的列:")
        for i, col in enumerate(self.df.columns):
            print(f"  {i}: {col}")
        
        while True:
            try:
                choice = input("请输入时间列的序号: ").strip()
                idx = int(choice)
                if 0 <= idx < len(self.df.columns):
                    self.time_col = self.df.columns[idx]
                    print(f"✅ 选择时间列: {self.time_col}")
                    break
                else:
                    print("❌ 序号超出范围，请重新输入")
            except ValueError:
                print("❌ 请输入有效的数字")
    
    def _process_time_column(self):
        """处理时间列"""
        if not self.time_col:
            raise ValueError("未找到时间列")
        
        try:
            print(f"🔧 正在处理时间列...")
            
            # 检查并修复24:00:00的问题
            if self.df[self.time_col].dtype == 'object':
                invalid_times = self.df[self.time_col].str.contains(' 24:00:00', na=False)
                if invalid_times.any():
                    print(f"   发现 {invalid_times.sum()} 个无效时间条目 (24:00:00)")
                    # 将24:00:00替换为00:00:00，并将日期加1天
                    mask = invalid_times
                    self.df.loc[mask, self.time_col] = self.df.loc[mask, self.time_col].str.replace(' 24:00:00', ' 00:00:00')
                    # 转换为datetime并加1天
                    temp_times = pd.to_datetime(self.df.loc[mask, self.time_col])
                    temp_times = temp_times + pd.Timedelta(days=1)
                    self.df.loc[mask, self.time_col] = temp_times.dt.strftime('%Y-%m-%d %H:%M:%S')
                    print(f"   已修复无效时间条目")
            
            # 转换为datetime
            self.df[self.time_col] = pd.to_datetime(self.df[self.time_col])
            print(f"✅ 时间列转换成功")
            
            # 添加月份列用于季节划分
            self.df['month'] = self.df[self.time_col].dt.month
            self.df['year'] = self.df[self.time_col].dt.year
            
            print(f"   时间范围: {self.df[self.time_col].min()} 到 {self.df[self.time_col].max()}")
            print(f"   数据年份: {sorted(self.df['year'].unique())}")
            
        except Exception as e:
            print(f"❌ 时间列处理失败: {str(e)}")
            raise
    
    def split_by_seasons(self):
        """按季节和年份划分数据"""
        print(f"\n🌍 开始按季节和年份划分数据...")

        seasonal_data = {}
        seasonal_stats = {}

        # 获取所有年份
        years = sorted(self.df['year'].unique())
        print(f"   数据包含年份: {years}")

        for year in years:
            year_data = self.df[self.df['year'] == year]

            for season_name, months in self.seasons.items():
                # 处理冬季跨年的特殊情况
                if season_name == 'Winter':
                    # 冬季包含12月(当年)和1-2月(次年)
                    if year == years[0]:
                        # 第一年：只取12月
                        season_mask = year_data['month'] == 12
                        season_key = f"{year}_Winter"
                    elif year == years[-1]:
                        # 最后一年：只取1-2月
                        season_mask = year_data['month'].isin([1, 2])
                        season_key = f"{year}_Winter"
                    else:
                        # 中间年份：取12月(当年)和1-2月(次年)
                        # 当年12月
                        current_winter = year_data[year_data['month'] == 12]
                        # 次年1-2月
                        next_year_data = self.df[self.df['year'] == year + 1]
                        next_winter = next_year_data[next_year_data['month'].isin([1, 2])]

                        if len(current_winter) > 0 or len(next_winter) > 0:
                            season_df = pd.concat([current_winter, next_winter], ignore_index=True)
                            season_df = season_df.sort_values(self.time_col)
                            season_key = f"{year}-{year+1}_Winter"

                            if len(season_df) > 0:
                                # 移除临时列
                                season_df_clean = season_df.drop(['month', 'year'], axis=1)
                                seasonal_data[season_key] = season_df_clean

                                seasonal_stats[season_key] = {
                                    'count': len(season_df_clean),
                                    'start_date': season_df_clean[self.time_col].min(),
                                    'end_date': season_df_clean[self.time_col].max(),
                                    'year_range': f"{year}-{year+1}"
                                }

                                print(f"  ❄️ {season_key:12s}: {len(season_df_clean):6,} 条记录 "
                                      f"({season_df_clean[self.time_col].min().strftime('%Y-%m-%d')} 到 "
                                      f"{season_df_clean[self.time_col].max().strftime('%Y-%m-%d')})")
                        continue
                else:
                    # 其他季节：正常处理
                    season_mask = year_data['month'].isin(months)
                    season_key = f"{year}_{season_name}"

                # 筛选该季节的数据
                season_df = year_data[season_mask].copy()

                if len(season_df) > 0:
                    # 移除临时添加的月份和年份列
                    season_df_clean = season_df.drop(['month', 'year'], axis=1)
                    seasonal_data[season_key] = season_df_clean

                    # 统计信息
                    seasonal_stats[season_key] = {
                        'count': len(season_df_clean),
                        'start_date': season_df_clean[self.time_col].min(),
                        'end_date': season_df_clean[self.time_col].max(),
                        'year_range': str(year)
                    }

                    # 选择合适的emoji
                    emoji = {'Spring': '🌸', 'Summer': '☀️', 'Autumn': '🍂', 'Winter': '❄️'}[season_name]
                    print(f"  {emoji} {season_key:12s}: {len(season_df_clean):6,} 条记录 "
                          f"({season_df_clean[self.time_col].min().strftime('%Y-%m-%d')} 到 "
                          f"{season_df_clean[self.time_col].max().strftime('%Y-%m-%d')})")
                else:
                    print(f"  ⚠️ {season_key:12s}: 无数据")

        return seasonal_data, seasonal_stats
    
    def save_seasonal_datasets(self, seasonal_data, seasonal_stats):
        """保存季节数据集"""
        print(f"\n💾 正在保存季节数据集...")

        saved_files = []

        for season_key, season_df in seasonal_data.items():
            # 生成文件名
            base_name = os.path.splitext(os.path.basename(self.input_file))[0]
            output_file = os.path.join(self.output_dir, f"{base_name}_{season_key.lower()}.csv")

            # 保存数据
            season_df.to_csv(output_file, index=False, encoding='utf-8-sig')
            saved_files.append(output_file)

            print(f"  ✅ {season_key:15s}: {output_file}")

        # 保存统计信息
        self._save_statistics(seasonal_stats)

        return saved_files
    
    def _save_statistics(self, seasonal_stats):
        """保存统计信息"""
        # 创建统计报告
        stats_lines = [
            "# 季节数据划分统计报告",
            f"**原始文件**: {self.input_file}",
            f"**划分时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"**总数据量**: {len(self.df):,} 条记录",
            "",
            "## 季节划分结果",
            "",
            "| 季节数据集 | 数据量 | 开始日期 | 结束日期 | 年份范围 |",
            "|------------|--------|----------|----------|----------|"
        ]

        # 按季节和年份排序
        sorted_seasons = sorted(seasonal_stats.items(), key=lambda x: (
            x[1]['start_date'] if 'start_date' in x[1] else datetime.min
        ))

        for season_key, stats in sorted_seasons:
            stats_lines.append(
                f"| {season_key} | {stats['count']:,} | "
                f"{stats['start_date'].strftime('%Y-%m-%d')} | "
                f"{stats['end_date'].strftime('%Y-%m-%d')} | {stats['year_range']} |"
            )

        stats_lines.extend([
            "",
            "## 季节定义",
            "- **春季 (Spring)**: 3-5月",
            "- **夏季 (Summer)**: 6-8月",
            "- **秋季 (Autumn)**: 9-11月",
            "- **冬季 (Winter)**: 12月-次年2月（跨年）",
            "",
            "## 划分说明",
            "- 每个季节按年份单独划分",
            "- 冬季特殊处理：包含当年12月和次年1-2月",
            "- 文件命名格式：`原文件名_年份_季节.csv`",
            "- 跨年冬季命名格式：`原文件名_年份1-年份2_winter.csv`",
            "",
            "## 输出文件",
        ])

        # 添加输出文件列表
        base_name = os.path.splitext(os.path.basename(self.input_file))[0]
        for season_key in sorted(seasonal_stats.keys()):
            output_file = f"{base_name}_{season_key.lower()}.csv"
            stats_lines.append(f"- {output_file}")

        # 保存统计报告
        stats_file = os.path.join(self.output_dir, "seasonal_split_report.md")
        with open(stats_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(stats_lines))

        print(f"  📊 统计报告: {stats_file}")
    
    def run_seasonal_split(self):
        """运行完整的季节划分流程"""
        print("🌍 开始季节数据划分...")
        
        # 加载数据
        if not self.load_data():
            return False
        
        # 按季节划分
        seasonal_data, seasonal_stats = self.split_by_seasons()
        
        if not seasonal_data:
            print("❌ 没有数据可以划分")
            return False
        
        # 保存数据集
        saved_files = self.save_seasonal_datasets(seasonal_data, seasonal_stats)
        
        print(f"\n🎉 季节划分完成！")
        print(f"📂 输出目录: {self.output_dir}")
        print(f"📄 生成文件数: {len(saved_files) + 1}")  # +1 for report
        
        return True


def main():
    """主函数"""
    print("🌍 季节数据划分工具")
    print("=" * 50)
    
    # 默认输入文件
    default_files = [
        "data/raw/Wind farm site 6 (Nominal capacity-96MW).csv",
        "data/raw/Wind farm site 1 (Nominal capacity-99MW).csv",
        "data/raw/Site_1_standardized.csv"
    ]
    
    # 检查默认文件
    input_file = None
    for file_path in default_files:
        if os.path.exists(file_path):
            input_file = file_path
            print(f"📁 使用默认文件: {input_file}")
            break
    
    if input_file is None:
        # 让用户输入文件路径
        input_file = input("请输入数据文件路径: ").strip()
        if not os.path.exists(input_file):
            print(f"❌ 文件不存在: {input_file}")
            return
    
    # 创建划分器并运行
    splitter = SeasonalDataSplitter(input_file)
    splitter.run_seasonal_split()


if __name__ == "__main__":
    main()
